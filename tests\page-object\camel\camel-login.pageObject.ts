class LoginCamelPageObject {

    get lbllogo_camel() { return $('//*[@title="Camel"]'); }
    get lbllogout_camel() { return $('(//span[contains(text(),"Logout")]//parent::*)[3]'); }
    get lblsignIn_camel() { return $('//*[text()="Sign in"]'); }
    get lnktobacco_camel() { return $('(//*[text()="Tobacco Rights"])'); }
    get lnktobaccopostlogin_camel() { return $('(//*[text()="Tobacco Rights"])[2]'); }
    get lnksustainability_camel() { return $('(//*[text()="Sustainability"])'); }
    get lnkcamelpoits_camel() { return $('(//*[text()="Camel Points Terms & Conditions"])'); }
     get lnkcamelpoitspostlogin_camel() { return $('(//*[text()="Camel Points Terms & Conditions"])[2]'); }
    get lblalreadyhaveaccount_camel() { return $('//p[@class="cmp-login__login-description"]'); }
    get lblforgot_camel() { return $('//div[@class="cmp-login__login-forgetCred"]'); }
    get lblloginemail_camel() { return $('//input[@id="loginUsername"]//preceding-sibling::label'); }
    get lblloginpassword_camel() { return $('//input[@id="loginPassword"]//preceding-sibling::label'); }
    get lblloginrememberme_camel() { return $('//input[@id="rememberMe"]//following-sibling::label'); }
    get lblregister_camel() { return $('//*[@class="cmp-login__login-heading"][contains(text(),"Register")]'); }
    get lblregisteraccount_camel() { return $('//div[@class="cmp-login__register-text"]'); }
    get lbljoinemail_camel() { return $('//*[@id="regEmail"]//preceding-sibling::label'); }
    get lbllegalnotice_camel() { return $('//div[@class="cmp-login__register-legalNotice"]'); }
    get lnkfaq_camel() { return $('//*[contains(text(),"FAQ")]'); }
    get lnksiteRequirement_camel() { return $('(//*[text()="Site Requirements"])'); }
    get lnkprivacyPolicy_camel() { return $('//*[contains(text(),"Privacy Policy")]'); }
    get lnkcontactUs_camel() { return $('(//*[text()="Contact Us"])'); }
    get lnktermsofUse_camel() { return $('(//*[text()="Terms of Use"])'); }
    get lnktextMessaging_camel() { return $('(//*[text()="Text Messaging Terms & Conditions"])'); }
    get lblcopyright_camel() { return $('(//div[@class="cmp-footer__copyright col"])[2]'); }
    get txtremembermeCheckbox_camel() { return $('//input[@id="rememberMe"]'); }
    get txtjoinnow_camel() { return $('//*[@class="cmp-login__register-link"]//*[@type="submit"]'); }
    get txtusername_camel() { return $('//input[@name="j_username"]'); }
    get txtpassword_camel() { return $('//input[@id="loginPassword"]'); }
    get btnlogin_camel() { return $('(//button[@type="submit"])[1]'); }
    get txtregemail_camel() { return $('//*[@id="regEmail"]'); }
    get lblFAQcontactUs_camel() { return $('(//h2[text()="Contact Us"])'); }

}
export default new LoginCamelPageObject();