import 'dotenv/config';
import updateDotenv from 'update-dotenv';
import readline from 'readline-sync';

/**
 * Get the Sauce Labs credentials from the environment variables.
 * If not available, throw an error in non-interactive environments (e.g., CI/CD pipelines).
 * In interactive environments (e.g., local development), prompt the user for credentials.
 */
export async function getSauceCredentials(): Promise<{
  sauceUsername: string;
  sauceAccessKey: string;
}> {
  let sauceUsername = process.env.SAUCE_USERNAME;
  let sauceAccessKey = process.env.SAUCE_ACCESS_KEY;

  // Check if the environment supports interactive input
  const isInteractive = process.stdin.isTTY;

  if (!sauceUsername) {
    if (isInteractive) {
      // Prompt the user for the Sauce Labs username (interactive mode)
      sauceUsername = readline.question('What is your Sauce Labs username? ');

      // Save the username to the .env file
      await updateDotenv({
        SAUCE_USERNAME: sauceUsername,
      });
      console.log('Sauce Labs username is saved in the .env file.');
    } else {
      // Non-interactive mode: throw an error if credentials are missing
      throw new Error(
        'Sauce Labs username is missing. Please set the SAUCE_USERNAME environment variable.',
      );
    }
  }

  if (!sauceAccessKey) {
    if (isInteractive) {
      // Prompt the user for the Sauce Labs API key (interactive mode)
      sauceAccessKey = readline.question('What is your Sauce Labs API key? ', {
        hideEchoBack: true,
      });

      // Save the API key to the .env file
      await updateDotenv({
        SAUCE_ACCESS_KEY: sauceAccessKey,
      });
      console.log('Sauce Labs API key is saved in the .env file.');
    } else {
      // Non-interactive mode: throw an error if credentials are missing
      throw new Error(
        'Sauce Labs API key is missing. Please set the SAUCE_ACCESS_KEY environment variable.',
      );
    }
  }

  return { sauceUsername, sauceAccessKey };
}