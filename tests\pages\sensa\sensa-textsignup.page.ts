import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaTextsignupPageObject from '../../page-object/sensa/sensa-textsignup.pageObject.ts';
import sensaAccountPage from './sensa-account.page.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import assertionHelper from '../../support/helpers/assertion-helper.ts';
import camelPromotionsPageObject from '../../page-object/camel/camel-promotions.pageObject.ts';


class TextSignUp {
    async clickontextSignUpLink() {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
                await elementActions.click(sensaTextsignupPageObject.lnktextsignup_sensa);
                await elementActions.assertion(sensaTextsignupPageObject.lblsignupTitle_sensa);
            } else {
                console.log('Text Sign Up Page is not found');
            }
            console.log('Navigated to Text Sign Up Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Text Sign Up Page', { error });
            throw error;
        }
    }

    async signUpPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const signuptitle = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignupTitle');
            const signupdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignupDescr');
            const textTitle = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextTitle');
            const mobileNumber = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobileNumber');
            const certify = testData.getCellValue(SHEET_NAME, scenarioname, 'lblCertify');
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblsignupTitle_sensa, signuptitle);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblsignupDescription_sensa, signupdesc);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lbltexttitle_sensa, textTitle);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblmobile_sensa, mobileNumber);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.txtiCertify_sensa, certify);
            await elementActions.assertion(sensaTextsignupPageObject.txtsubmit_sensa);
            console.log('Validated the Content in  Text Sign Up page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in  Text Sign Up page', { error });
            throw error;
        }
    }

    async entervalidMobileNumberandvalidatesuccessmssg(mobileNumber: string, successmssg: string) {
        try {
            await elementActions.waitForDisplayed(sensaTextsignupPageObject.txtmobilephone_sensa);
            await elementActions.setValue(sensaTextsignupPageObject.txtmobilephone_sensa, mobileNumber);
            await elementActions.click(sensaTextsignupPageObject.txtiCertify_sensa);
            await elementActions.click(sensaTextsignupPageObject.txtsubmit_sensa);
            const url = browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparisionremovelinespace(camelPromotionsPageObject.lblsuccess_camel, successmssg);
            } else {
                await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblsuccessmssg_sensa, successmssg);
            }
            console.log('Navigated to Text Sign Up Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Text Sign Up Page', { error });
            throw error;
        }
    }

    async enterinvalidMobileNumberandvalidateerror(mobileNumber: string, error: string) {
        try {
            if (driver.isIOS) {
                await elementActions.click(sensaTextsignupPageObject.txtmobilephone_sensa);
                await $('body').click();
            } else {
                await sensaTextsignupPageObject.txtmobilephone_sensa.doubleClick();
                await $('body').doubleClick();
            }
            await assertionHelper.assertElementDisabled(sensaTextsignupPageObject.txtsubmit_sensa);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblvalidationError_sensa, error);
            await elementActions.setValue(sensaTextsignupPageObject.txtmobilephone_sensa, mobileNumber);
            await elementActions.clickusingJavascript(sensaTextsignupPageObject.txtiCertify_sensa);
            await elementActions.clickusingJavascript(sensaTextsignupPageObject.txtsubmit_sensa);
            await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lblvalidationError_sensa, error);
            console.log('Navigated to Text Sign Up Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Text Sign Up Page', { error });
            throw error;
        }
    }

}
export default new TextSignUp();
