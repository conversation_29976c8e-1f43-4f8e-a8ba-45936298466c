const fs = require('fs');
const { google } = require('googleapis');
const readline = require('readline');

// Path to your credentials file
const CREDENTIALS_PATH = './tests/resources/google-key/gmailCredentials.json';
const TOKEN_PATH = './tests/resources/google-key/token.json';

// Gmail API scope
const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];

/**
 * Create an OAuth2 client with the given credentials, and then execute the
 * given callback function.
 */
async function authorize() {
    try {
        // Load client secrets from a local file.
        const credentials = JSON.parse(fs.readFileSync(CREDENTIALS_PATH));
        const { client_secret, client_id, redirect_uris } = credentials.installed;
        const oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

        // Check if we have previously stored a token.
        try {
           const tokenFileContent = fs.readFileSync(TOKEN_PATH, 'utf8');
            const token = JSON.parse(tokenFileContent);

            // A basic check: ensure refresh_token exists, as we request 'offline' access.
            if (!token.refresh_token) {
                console.log(`⚠️ Token file at ${TOKEN_PATH} is missing a refresh_token.`);
                console.log('   A new token with offline access will be generated.');
                return await getNewToken(oAuth2Client);
            }

            oAuth2Client.setCredentials(token);
            console.log(`ℹ️ Token file found at ${TOKEN_PATH}. Attempting to use existing token.`);
            return oAuth2Client;
        } catch (err) {
            if (err.code === 'ENOENT') {
                // This is the normal case for first run or after deleting token.json
                console.log(`🔑 No token file found at ${TOKEN_PATH}, generating new one...`);
            } else if (err instanceof SyntaxError) {
                console.warn(`⚠️ Error parsing token file at ${TOKEN_PATH}. The file might be corrupted.`);
                console.log('   A new token will be generated. The corrupted file will be overwritten.');
            } else {
                // Other fs errors (permissions, etc.)
                console.warn(`⚠️ Could not read token file at ${TOKEN_PATH}: ${err.message}`);
                console.log('   Attempting to generate a new token...');
            }
            return await getNewToken(oAuth2Client);
        }
    } catch (error) {
        console.error('❌ Error loading credentials:', error);
        throw error;
    }
}

/**
 * Get and store new token after prompting for user authorization.
 */
async function getNewToken(oAuth2Client) {
    const authUrl = oAuth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: SCOPES,
        response_type: 'code', // Explicitly set response_type
    });

    console.log('\n--- Generated Auth URL (Please examine this URL carefully) ---');
    console.log(authUrl);
    console.log('--- END DEBUG ---');

    console.log('\n🌐 Please visit this URL to authorize the application:');
    console.log('📋 Copy and paste this URL in your browser:');
    console.log(authUrl);
    console.log('\n');

    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
    });

    return new Promise((resolve, reject) => {
        rl.question('📝 Enter the authorization code from the browser: ', (code) => {
            rl.close();
            oAuth2Client.getToken(code, (err, token) => {
                if (err) {
                    console.error('❌ Error retrieving access token:', err);
                    reject(err);
                    return;
                }
                oAuth2Client.setCredentials(token);

                // Store the token to disk for later program executions
                try {
                    fs.writeFileSync(TOKEN_PATH, JSON.stringify(token));
                    console.log('✅ Token stored successfully at:', TOKEN_PATH);
                    console.log('🎉 Gmail API authorization complete!');
                    resolve(oAuth2Client);
                } catch (err) {
                    console.error('❌ Error storing token:', err);
                    reject(err);
                }
            });
        });
    });
}

/**
 * Test the Gmail API connection
 */
async function testGmailConnection(auth) {
    try {
        const gmail = google.gmail({ version: 'v1', auth });
        const res = await gmail.users.labels.list({
            userId: 'me',
        });

        console.log('\n📧 Gmail API Test Results:');
        console.log('✅ Successfully connected to Gmail API');
        console.log('📁 Available labels:', res.data.labels.map(label => label.name).slice(0, 5).join(', '), '...');

        // Test getting recent emails
        const messages = await gmail.users.messages.list({
            userId: 'me',
            maxResults: 5,
        });

        console.log(`📬 Found ${messages.data.messages ? messages.data.messages.length : 0} recent messages`);
        console.log('🎯 Gmail service is ready for WebDriverIO tests!');

    } catch (error) {
        console.error('❌ Error testing Gmail connection:', error);
        throw error;
    }
}

// Main execution
async function main() {
    console.log('🚀 Gmail Token Generator for WebDriverIO Tests');
    console.log('===============================================\n');

    try {
        const auth = await authorize();
        await testGmailConnection(auth);

        console.log('\n✨ Setup Complete!');
        console.log('📝 You can now run your WebDriverIO Gmail tests');
        console.log('🏃‍♂️ Try running: npm run test:gmail');

    } catch (error) {
        // Check if it's the specific GaxiosError for invalid_grant (token expired/revoked)
        if (error.name === 'GaxiosError' &&
            error.response &&
            error.response.data &&
            error.response.data.error === 'invalid_grant' &&
            error.response.data.error_description &&
            error.response.data.error_description.includes('Token has been expired or revoked')) {

            console.error('\n❌ Gmail API Error: The stored token is no longer valid (expired or revoked).');
            console.log(`ℹ️ This often happens if the application's access was manually revoked from your Google account,
   or if the OAuth consent screen in Google Cloud Console is in "Testing" mode and the refresh token expired (typically after 7 days).`);
            console.log('\n👉 Action Required:');
            console.log(`   1. Delete the existing token file: ${TOKEN_PATH}`);
            console.log('   2. Re-run this script: node generate-gmail-token.cjs');
            console.log(`   3. If this issue persists weekly, check your app's OAuth consent screen status in Google Cloud Console.
      Consider adding your email as a "Test User" or publishing the app.`);
        } else {
            console.error('\n💥 Setup failed with an unexpected error:');
            console.error(error); // Log the full error for other unexpected issues
        }
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { authorize, testGmailConnection };
