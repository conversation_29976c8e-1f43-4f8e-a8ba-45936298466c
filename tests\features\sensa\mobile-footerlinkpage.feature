Feature: Footerlink Pages Validation.

    Scenario Outline: Vaidate FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on FAQ footerlink
        Then The user Validates FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginFAQFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname            |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @SensaPreLoginFAQFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | filename              | sheetname             | scenarioname            |
            | Sensa | https://www.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Contact us footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Contact Us footerlink
        Then The user Validates contact <PERSON> with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginContactUsFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname            |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @SensaPreLoginContactUsFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | filename              | sheetname             | scenarioname            |
            | Sensa | https://www.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Site Requirement footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Site Requirement footerlink
        Then The user Validates Site Requirement Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginSiteRequirementFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname                         |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

        @SensaPreLoginSiteRequirementFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | filename              | sheetname             | scenarioname                         |
            | Sensa | https://www.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

    Scenario Outline: Vaidate Terms Of Use footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Terms Of Use footerlink
        Then The user Validates Terms Of Use Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginTermsOfUseFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname                     |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

        @SensaPreLoginTermsOfUseFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | filename              | sheetname             | scenarioname                     |
            | Sensa | https://www.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

    Scenario Outline: Vaidate Privacy Policy footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Privacy Policy footerlink
        Then The user Validates Privacy Policy Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginPrivacyPolicyFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname                       |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

        @SensaPreLoginPrivacyPolicyFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname                       |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

    Scenario Outline: Vaidate Text Messaging footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Text Messaging footerlink
        Then The user Validates Text Messaging Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPreLoginTextMessagingFooterlink_Validation_QA
        Examples:
            | Brand | URL                             | filename              | sheetname             | scenarioname                       |
            | Sensa | https://aem-stage.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Text Messaging footerlink |

        @SensaPreLoginTextMessagingFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | filename              | sheetname             | scenarioname                       |
            | Sensa | https://www.sensavape.com | aem-mobile-sensa.json | Pre-login Footerlinks | Validate Text Messaging footerlink |


    Scenario Outline: Vaidate Contact us footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login Contact Us footerlink
        Then The user Validates post login contact Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginContactUsFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                               |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate post login contact us  footerlink |

        @SensaPostLoginContactUsFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                               |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate post login contact us  footerlink |


    Scenario Outline: Vaidate FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login FAQ footerlink
        Then The user Validates FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginFAQFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                       |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate FAQ post login footerlink |

        @SensaPostLoginFAQFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                       |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate FAQ post login footerlink |

    Scenario Outline: Vaidate Site Requirements footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login Site Requirements footerlink
        Then The user Validates Site Requirement Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginSiteRequirementFooterlink_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                                    |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate SiteRequirements post login footerlink |

        @SensaPostLoginSiteRequirementFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                                    |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate SiteRequirements post login footerlink |

    Scenario Outline: Vaidate Terms Of Use footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login Terms Of Use footerlink
        Then The user Validates Terms Of Use Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginTermsOfUseFooterlink_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                                |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Terms of Use post login footerlink |

        @SensaPostLoginTermsOfUseFooterlink_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                                |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Terms of Use post login footerlink |

    Scenario Outline: Vaidate Privacy Policy footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login Privacy Policy footerlink
        Then The user Validates Privacy Policy Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginPrivacyPolicyFooterlink_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                                   |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Privacy Policy  post login footerlink |

        @SensaPostLoginPrivacyPolicyFooterlink_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                                   |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Privacy Policy  post login footerlink |

    Scenario Outline: Vaidate Text Messaging footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on post login Text Messaging footerlink
        Then The user Validates Text Messaging Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @SensaPostLoginextMessagingFooterlink_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname              | scenarioname                                   |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Text Messaging  post login footerlink |

        @SensaPostLoginextMessagingFooterlink_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname              | scenarioname                                   |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Post-login Footerlinks | Validate Text Messaging  post login footerlink |

