import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import { expect } from '@wdio/globals';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';


class AccountPage {


    async loginwithcredential(username: string, password: string) {
        try {
            await elementActions.waitForDisplayed(RegistrationPageObject.lblsignIn_sensa);
            await elementActions.setValue(AccountPageObject.txtusername_sensa, username);
            await elementActions.setValue(AccountPageObject.txtpassword_sensa, password);
            await elementActions.clickusingJavascript(AccountPageObject.btnlogin_sensa);
            console.log('Logged in Successfully');
        } catch (error) {
            logger.error('Failed to Login', { error });
            throw error;
        }
    }


    async clickonaccountlinkfromheader() {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.btnhamburgerMenu_sensa);
            const url = browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.clickusingJavascript(AccountPageObject.lnkaccountProd_sensa);
            } else {
                if ((await url).includes('aem')) {
                    await elementActions.waitForDisplayed(AccountPageObject.lnkaccount_sensa);
                    await elementActions.clickusingJavascript(AccountPageObject.lnkaccount_sensa);
                } else {
                    await elementActions.waitForDisplayed(AccountPageObject.lnkaccountProd_sensa);
                    await elementActions.clickusingJavascript(AccountPageObject.lnkaccountProd_sensa);
                }
            }
            await elementActions.assertion(AccountPageObject.lblaccountheader_sensa);
            console.log('Navigated to Account Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigated to Account Page', { error });
            throw error;
        }
    }

    async clickoncontactinfo() {
        try {
            await elementActions.click(AccountPageObject.lnkcontact_sensa);
            await elementActions.assertion(AccountPageObject.lbllegalName_sensa);
            console.log('Navigated to Contact Info Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Contact Info Page', { error });
            throw error;
        }
    }
    async contactinfoUpdate(mobile: string, currentaddr: string, zipcode: string) {
        try {
            await elementActions.waitForDisplayed(AccountPageObject.txtcurrentAddr_sensa);
            await elementActions.setValue(AccountPageObject.txtcurrentAddr_sensa, currentaddr);
            await elementActions.setValue(AccountPageObject.txtzipCode_sensa, zipcode);
            await elementActions.setValue(AccountPageObject.txtmobile_sensa, mobile);
            await elementActions.clickusingJavascript(AccountPageObject.btnupdate_sensa);
            console.log('Updated Contact Info details Successfully');
        } catch (error) {
            logger.error('Failed to Update to Contact Info details', { error });
            throw error;
        }
    }
    async contactinfoUpdateMessage(updateMessage: string) {
        try {
            await this.mssgcomparision(AccountPageObject.txtsuccessmessage_sensa, updateMessage);
            console.log('Success Message is Displayed Successfully');
        } catch (error) {
            logger.error('Success Message is not Displayed', { error });
            throw error;
        }
    }
    async contactinfoValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const legalName = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalname');
            const govtIssue = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgovtissue');
            const pleasecontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpleasecontact');
            const pleasecontact_prod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpleasecontact_Prod');
            const currentAddr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcurrentaddr');
            const mobileNumber = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobile');
            await this.mssgcomparision(AccountPageObject.lbllegalName_sensa, legalName);
            await this.mssgcomparision(AccountPageObject.lblgovtissue_sensa, govtIssue);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await this.mssgcomparision(AccountPageObject.lblnamemiss_sensa, pleasecontact);
            } else {
                await this.mssgcomparision(AccountPageObject.lblnamemiss_sensa, pleasecontact_prod);
            }
            await elementActions.assertion(AccountPageObject.txtfirstName_sensa);
            await elementActions.assertion(AccountPageObject.txtlastName_sensa);
            await this.mssgcomparision(AccountPageObject.lblcurrentAddr_sensa, currentAddr);
            await elementActions.assertion(AccountPageObject.txtcurrentAddr_sensa);
            await elementActions.assertion(AccountPageObject.txtaptOpt_sensa);
            await elementActions.assertion(AccountPageObject.txtzipCode_sensa);
            await elementActions.assertion(AccountPageObject.txtcity_sensa);
            await elementActions.assertion(AccountPageObject.txtstate_sensa);
            await this.mssgcomparision(AccountPageObject.lblmobileNumber_sensa, mobileNumber);
            await elementActions.assertion(AccountPageObject.txtmobile_sensa);
            await elementActions.assertion(AccountPageObject.btnupdate_sensa);

            console.log('Validated the Content in Contact info page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Contact info page', { error });
            throw error;
        }
    }
    async cickonemailpagelink() {
        try {
            await elementActions.click(AccountPageObject.lnkemail_sensa);
            await elementActions.assertion(AccountPageObject.lblcurrentEmail_sensa);
            console.log('Navigated to Email Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Email Page', { error });
            throw error;
        }
    }

    async emailerrorMssg(newEmail: string, confirmEmail: string, errormssg: string) {
        try {
            await elementActions.setValue(AccountPageObject.txtnewEmail_sensa, newEmail);
            await elementActions.setValue(AccountPageObject.txtcnewEmail_sensa, confirmEmail);
            await elementActions.click(AccountPageObject.btnemailUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblemailerrormssg_sensa, errormssg);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async emailPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const currentemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcurrentemail');
            const newemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewemail');
            const confirmemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblconfirmemail');
            const subscription = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsubscription');
            const stayinloop = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstayinloop');
            const camel = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcamel');
            const snus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsnus');
            const cougar = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcougar');
            const grizzly = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgrizzly');
            const grizzlysnus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgrizzlysnus');
            const grizzlynmo = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgrizzlynmo');
            const kodiak = testData.getCellValue(SHEET_NAME, scenarioname, 'lblkodiak');
            const levigarrett = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllevigarrett');
            const luckystrike = testData.getCellValue(SHEET_NAME, scenarioname, 'lblluckystrike');
            const nas = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnas');
            const newport = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewport');
            const pallmall = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpallmall');
            const sensa = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsensa');
            const velo = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvelo');
            const vuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvuse');
            const btnupdate = testData.getCellValue(SHEET_NAME, scenarioname, 'btnupdateemail');
            await this.mssgcomparision(AccountPageObject.lblcurrentEmail_sensa, currentemail);
            await this.mssgcomparision(AccountPageObject.lblnewEmail_sensa, newemail);
            await this.mssgcomparision(AccountPageObject.lblconfirmEmail_sensa, confirmemail);
            await elementActions.assertion(AccountPageObject.txtcurrentEmail_sensa);
            await elementActions.assertion(AccountPageObject.txtnewEmail_sensa);
            await elementActions.assertion(AccountPageObject.txtcnewEmail_sensa);
            await this.mssgcomparision(AccountPageObject.lblsubscription_sensa, subscription);
            await this.mssgcomparision(AccountPageObject.lblstayloop_sensa, stayinloop);
            await this.mssgcomparision(AccountPageObject.lblcamecheckbox_sensa, camel);
            await this.mssgcomparision(AccountPageObject.lblcamesnuscheckbox_sensa, snus);
            await this.mssgcomparision(AccountPageObject.lblcougarcheckbox_sensa, cougar);
            await this.mssgcomparision(AccountPageObject.lblgrizzlycheckbox_sensa, grizzly);
            await this.mssgcomparision(AccountPageObject.lblgrizzlysnuscheckbox_sensa, grizzlysnus);
            await this.mssgcomparision(AccountPageObject.lblgrizzlyNicotinecheckbox_sensa, grizzlynmo);
            await this.mssgcomparision(AccountPageObject.lblkodiakcheckbox_sensa, kodiak);
            await this.mssgcomparision(AccountPageObject.lblleviagrrettcheckbox_sensa, levigarrett);
            await this.mssgcomparision(AccountPageObject.lblluckystrikecheckbox_sensa, luckystrike);
            await this.mssgcomparision(AccountPageObject.lblnascheckbox_sensa, nas);
            await this.mssgcomparision(AccountPageObject.lblnewportcheckbox_sensa, newport);
            await this.mssgcomparision(AccountPageObject.lblpallmallcheckbox_sensa, pallmall);
            await this.mssgcomparision(AccountPageObject.lblsensacheckbox_sensa, sensa);
            await this.mssgcomparision(AccountPageObject.lblvelocheckbox_sensa, velo);
            await this.mssgcomparision(AccountPageObject.lblvusecheckbox_sensa, vuse);
            await this.mssgcomparision(AccountPageObject.btnemailUpdate_sensa, btnupdate);

            console.log('Validated the Content in Email page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Email page', { error });
            throw error;
        }
    }

    async emailupdatesuccessfull(newEmail: string, successmssg: string) {
        try {
            await elementActions.setValue(AccountPageObject.txtnewEmail_sensa, newEmail);
            await elementActions.setValue(AccountPageObject.txtcnewEmail_sensa, newEmail);
            await elementActions.click(AccountPageObject.btnemailUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblemailsuccessmssg_sensa, successmssg);
            console.log('Email Updated Successfully');
        } catch (error) {
            logger.error('Failed to Update Email', { error });
            throw error;
        }
    }

    async subscriptionupdatesuccessfull(successmssg: string) {
        try {
            await elementActions.click(AccountPageObject.btnemailUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblsubscriptionsuccessmssg_sensa, successmssg);
            console.log('Subscriptions Updated Successfully');
        } catch (error) {
            logger.error('Failed to Update Subscription', { error });
            throw error;
        }


    }
    async loginErrorMessage(errormessage: string) {
        try {
            await this.mssgcomparision(AccountPageObject.lblloginerror_sensa, errormessage);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }


    }

    async clickonsecuritypagelink() {
        try {
            await elementActions.click(AccountPageObject.lnksecurity_sensa);
            await elementActions.assertion(AccountPageObject.txtcurrentPassword_sensa);
            console.log('Navigated to Security Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Security Page', { error });
            throw error;
        }
    }

    async securityPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const password = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
            const passwordstatement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordstatement');
            const challengequestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchallengequestion');
            const challengequestionstatement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchallengequestionstatement');
            await this.mssgcomparision(AccountPageObject.lblpassword_sensa, password);
            await this.mssgcomparision(AccountPageObject.lblpasswordconditions_sensa, passwordstatement);
            await this.mssgcomparision(AccountPageObject.lblchallengequestion_sensa, challengequestion);
            await this.mssgcomparision(AccountPageObject.lblchallengequestionconditions_sensa, challengequestionstatement);
            await elementActions.assertion(AccountPageObject.txtcurrentPassword_sensa);
            await elementActions.assertion(RegistrationPageObject.txtpassword_sensa);
            await elementActions.assertion(RegistrationPageObject.txtconfirmPassword_sensa);
            await elementActions.assertion(RegistrationPageObject.txtsecurityQuestion_sensa);
            await elementActions.assertion(RegistrationPageObject.txtsecuritynswer_sensa);
            await elementActions.assertion(AccountPageObject.btnpasswordUpdate_sensa);
            await elementActions.assertion(AccountPageObject.btnsecurityanswerUpdate_sensa);
            const handles = browser.getWindowHandles();
            console.log('handles', handles);
            console.log('Validated the Content in Security page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Security page', { error });
            throw error;
        }
    }


    async passworderrorMssg(currentPassword: string, newPassword: string, confirmnewPassword: string, errormssg: string) {
        try {
            await elementActions.setValue(AccountPageObject.txtcurrentPassword_sensa, currentPassword);
            await elementActions.setValue(RegistrationPageObject.txtpassword_sensa, newPassword);
            await elementActions.setValue(RegistrationPageObject.txtconfirmPassword_sensa, confirmnewPassword);
            await elementActions.clickusingJavascript(AccountPageObject.btnpasswordUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblpassworderrormssg_sensa, errormssg);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async samepassworderrorMssg(currentPassword: string, errormssg: string) {
        try {
            await elementActions.setValue(AccountPageObject.txtcurrentPassword_sensa, currentPassword);
            await elementActions.setValue(RegistrationPageObject.txtpassword_sensa, currentPassword);
            await elementActions.setValue(RegistrationPageObject.txtconfirmPassword_sensa, currentPassword);
            await elementActions.clickusingJavascript(AccountPageObject.btnpasswordUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblsamepassworderrormssg_sensa, errormssg);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async passwordsuccessMssg(currentPassword: string, newPassword: string, successmessage: string) {
        try {
            await elementActions.setValue(AccountPageObject.txtcurrentPassword_sensa, currentPassword);
            await elementActions.setValue(RegistrationPageObject.txtpassword_sensa, newPassword);
            await elementActions.setValue(RegistrationPageObject.txtconfirmPassword_sensa, newPassword);
            await elementActions.clickusingJavascript(AccountPageObject.btnpasswordUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblpasswordsuccessmssg_sensa, successmessage);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async securityquestionupdate(securityquestion: string, answer: string, successmessage: string) {
        try {
            (await RegistrationPageObject.txtsecurityQuestion_sensa).selectByVisibleText(securityquestion);
            await elementActions.setValue(RegistrationPageObject.txtsecuritynswer_sensa, answer);
            await elementActions.clickusingJavascript(AccountPageObject.btnsecurityanswerUpdate_sensa);
            await this.mssgcomparision(AccountPageObject.lblsecuritysuccessmssg_sensa, successmessage);
            console.log('Validated Error Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Error Message', { error });
            throw error;
        }
    }

    async mssgcomparision(element: ChainablePromiseElement, mssg: string) {
        try {
            await elementActions.waitForDisplayed(element);
            await element.highlight();
            const successMessage = (element).getText();
            const actualsuccessmessage = (await successMessage).trim();
            const expectedoutput = (await mssg).trim();
            console.log('Generated Text is: ', actualsuccessmessage);
            console.log('Expected Text is: ', mssg);
            expect(actualsuccessmessage).toEqual(expectedoutput);
            console.log('Validated Message Successfully');
        } catch (error) {
            console.log('Failed to Validate Message', { error });
            throw error;
        }
    }

    async mssgcomparisionremovelinespace(element: ChainablePromiseElement, mssg: string) {
        try {
            await elementActions.waitForDisplayed(element);
            await element.highlight();
            const actualsuccessmessage = (await (await element.getText()).replace(/\s+/g, ' ').trim());
            const expectedoutput = (await mssg).trim();
            console.log('Generated Text is: ', actualsuccessmessage);
            console.log('Expected Text is: ', mssg);
            expect(actualsuccessmessage).toEqual(expectedoutput);
            console.log('Validated Message Successfully');
        } catch (error) {
            console.log('Failed to Validate Message', { error });
            throw error;
        }
    }


    async clickontobaccoPreferencesPagelink() {
        try {
            await elementActions.click(AccountPageObject.lnktobaccoPreferences_sensa);
            await elementActions.assertion(AccountPageObject.lbltellus_sensa);
            console.log('Navigated to Tobacco Preferences Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Tobacco Preferences Page', { error });
            throw error;
        }
    }

    async tobaccoprferenceproductselection(productName: string) {
        try {
            const url = browser.getUrl();
            await elementActions.assertion(sensaAccountPageObject.gettxtproductcheckbox_sensa(productName));
            const productcheckbox = await sensaAccountPageObject.productcheckboxes_sensa;
            if ((await url).includes('camel')) {
                for (const checkbox of productcheckbox) {
                    const isChecked = await browser.execute(el => el instanceof HTMLInputElement ? el.checked : false, checkbox);
                    if (isChecked) {
                        await driver.execute('arguments[0].click();', checkbox);
                        console.log('Unselected one checkbox');
                    }
                }
            } else {

                for (const checkboxes of productcheckbox) {
                    if (await checkboxes.isSelected()) {
                        if (driver.isIOS) {
                            await checkboxes.click();
                        } else {
                            await elementActions.clickusingJavascript(checkboxes);
                        }
                    }
                }
            }
            await elementActions.clickusingJavascript(sensaAccountPageObject.gettxtproductcheckbox_sensa(productName));
            console.log('Selected Tobacco Products');
        } catch (error) {
            logger.error('Failed to Selected Tobacco Products', { error });
            throw error;
        }
    }

    async tobaccoprferenceupdate(brand: string, flavour: string, purchases: string, preferednicotine: string, regularcut: string, product: string) {
        try {
            // #{products are "CIGARETTES", "VAPOR", "NICOTINE POUCHES", "MOIST SNUFF/DIP", "SNUS"}
            const productName = product.toUpperCase();
            if (productName === 'CIGARETTES') {
                await this.dropdownselection(sensaAccountPageObject.txttobaccodrpdown_sensa);
                (await sensaAccountPageObject.txtcigarrettbranddropdown_sensa).selectByVisibleText(brand);
                (await sensaAccountPageObject.txtcigarrettflavordropdown_sensa).selectByVisibleText(flavour);
                (await sensaAccountPageObject.txtcigarrettflavordropdown_sensa).selectByVisibleText(purchases);
            } else if (productName === 'VAPOR') {
                await this.dropdownselection(sensaAccountPageObject.txttobaccodrpdown_sensa);
                (await sensaAccountPageObject.txtvaporbranddropdown_sensa).selectByVisibleText(brand);
                (await sensaAccountPageObject.txtvaporflavordropdown_sensa).selectByVisibleText(flavour);
                (await sensaAccountPageObject.txtvapornicotinepreferencedropdown_sensa).selectByVisibleText(preferednicotine);
                (await sensaAccountPageObject.txtvaporpurchasesropdown_sensa).selectByVisibleText(purchases);
                const vapecheckbox = await sensaAccountPageObject.txtvapecheckbox_sensa;
                for (let i = 0; i < await vapecheckbox.length; i++) {
                    const checkboxes = vapecheckbox[i];
                    await checkboxes.scrollIntoView({ block: 'center', inline: 'center' });
                    if (!await checkboxes.isSelected()) {
                        checkboxes.click();
                    }
                    if (await checkboxes.isSelected()) {
                        checkboxes.click();
                    }
                }
            } else if (productName === 'NICOTINE POUCHES') {
                await this.dropdownselection(sensaAccountPageObject.txttobaccodrpdown_sensa);
                (await sensaAccountPageObject.txtnicotinebranddropdown_sensa).selectByVisibleText(brand);
                (await sensaAccountPageObject.txtnicotineflavordropdown_sensa).selectByVisibleText(flavour);
                (await sensaAccountPageObject.txtnicotinepurchasesropdown_sensa).selectByVisibleText(purchases);
            } else if (productName === 'MOIST SNUFF/DIP') {
                await this.dropdownselection(sensaAccountPageObject.txttobaccodrpdown_sensa);
                (await sensaAccountPageObject.txtmoistbranddropdown_sensa).selectByVisibleText(brand);
                (await sensaAccountPageObject.txtmoistflavordropdown_sensa).selectByVisibleText(flavour);
                (await sensaAccountPageObject.txtmoistregularcutdropdown_sensa).selectByVisibleText(regularcut);
                (await sensaAccountPageObject.txtmoistpurchasesropdown_sensa).selectByVisibleText(purchases);

            } else if (productName === 'SNUS') {
                await this.dropdownselection(sensaAccountPageObject.txttobaccodrpdown_sensa);
                (await sensaAccountPageObject.txtsnusbranddropdown_sensa).selectByVisibleText(brand);
                (await sensaAccountPageObject.txtsnusflavordropdown_sensa).selectByVisibleText(flavour);
                (await sensaAccountPageObject.txtsnuspurchasesropdown_sensa).selectByVisibleText(purchases);
            }
            const url = browser.getUrl();
            if ((await url).includes('registration')) {
                await elementActions.click(sensaRegistrationPageObject.btnsave_sensa);
            } else {
                await elementActions.click(sensaAccountPageObject.btnsurveyUpdate_sensa);
            }
            console.log('Updated Tobacco Preferences Successfully');
        } catch (error) {
            logger.error('Failed to Update Tobacco Preferences', { error });
            throw error;
        }
    }
    async dropdownselection(element: ChainablePromiseElement) {
        try {

            const dropdowns = await element;

            for (let i = 0; i < dropdowns.length; i++) {
                const dropdown = dropdowns[i];
                const isDisplayed = await dropdown.isDisplayed();
                if (isDisplayed) {
                    await dropdown.scrollIntoView({ block: 'center', inline: 'center' });
                    const options = await dropdown.$$('option');
                    for (let j = 0; j < options.length; j++) {
                        await dropdown.selectByIndex(j);
                    }

                }
            }
            console.log('Validated Dropdown Successfully');
        } catch (error) {
            logger.error('Failed to Validate Dropdown', { error });
            throw error;
        }
    }

    async tobaccoSuccessMessage(successmssg: string) {
        try {
            await this.mssgcomparision(AccountPageObject.lbltobaccoSurveymssg_sensa, successmssg);
            console.log('Validated success Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate success Message', { error });
            throw error;
        }


    }

    async clickonretailAccountpagelink() {
        try {
            await elementActions.click(AccountPageObject.lnkretailAccount_sensa);
            await elementActions.assertion(AccountPageObject.txtrewardscheckbox_sensa);
            console.log('Navigated to Retailer Account Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Retailer Account', { error });
            throw error;
        }
    }

    async retailAccountoptin(successmssg: string) {
        try {
            const checkboxe = await sensaAccountPageObject.txtcheckboxes_sensa;
            for (let i = 0; i < await checkboxe.length; i++) {
                const checkboxes = checkboxe[i];
                await checkboxes.scrollIntoView({ block: 'center', inline: 'center', behavior: 'smooth' });
                if (await checkboxes.isSelected()) {
                    checkboxes.click();
                }
            }
            await elementActions.click(AccountPageObject.txtrewardscheckbox_sensa);
            await this.mssgcomparision(AccountPageObject.lbllinksuccessmssg_sensa, successmssg);
            const url = driver.getUrl();
            if ((await url).includes('aem')) {
                await elementActions.click(AccountPageObject.txtkwickcheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lbllinksuccessmssg_sensa, successmssg);
            } else {
                await elementActions.click(AccountPageObject.txtcirclekcheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lbllinksuccessmssg_sensa, successmssg);
                await elementActions.click(AccountPageObject.txtxspeedycheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lbllinksuccessmssg_sensa, successmssg);
                await elementActions.click(AccountPageObject.txtmurphytextbox_sensa);
                await this.mssgcomparision(AccountPageObject.lbllinksuccessmssg_sensa, successmssg);
            }

            console.log('Opted in  Successfully');
        } catch (error) {
            logger.error('Failed to Opt in', { error });
            throw error;
        }
    }

    async retailAccountoptout(successmssg: string) {
        try {
            await elementActions.click(AccountPageObject.txtrewardscheckbox_sensa);
            await this.mssgcomparision(AccountPageObject.lblunlinksuccessmssg_sensa, successmssg);
            const url = driver.getUrl();
            if ((await url).includes('aem')) {
                await elementActions.click(AccountPageObject.txtkwickcheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lblunlinksuccessmssg_sensa, successmssg);
            } else {
                await elementActions.assertion(AccountPageObject.txtrewardscheckbox_sensa);
                await elementActions.click(AccountPageObject.txtcirclekcheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lblunlinksuccessmssg_sensa, successmssg);
                await elementActions.click(AccountPageObject.txtxspeedycheckbox_sensa);
                await this.mssgcomparision(AccountPageObject.lblunlinksuccessmssg_sensa, successmssg);
                await elementActions.click(AccountPageObject.txtmurphytextbox_sensa);
                await this.mssgcomparision(AccountPageObject.lblunlinksuccessmssg_sensa, successmssg);
            }
            console.log('Opted out  Successfully');
        } catch (error) {
            logger.error('Failed to Opt out', { error });
            throw error;
        }
    }
    async clickoncouponsPagelink() {
        try {
            await elementActions.click(AccountPageObject.lnkcoupons_sensa);
            await elementActions.assertion(AccountPageObject.lblavailableCoupons_sensa);
            console.log('Navigated to Coupons Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Coupons Page', { error });
            throw error;
        }
    }

    async couponsPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const availablecoupons = testData.getCellValue(SHEET_NAME, scenarioname, 'lblavailableCoupons');
            const savingstext = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsavings');
            const claimedcoupons = testData.getCellValue(SHEET_NAME, scenarioname, 'lblclaimedcoupons');
            await this.mssgcomparision(AccountPageObject.lblsavings_sensa, savingstext);
            await this.mssgcomparision(AccountPageObject.lblclaimedmobilecoupons_sensa, claimedcoupons);
            await elementActions.assertion(AccountPageObject.lblcouponscount_sensa);
            await elementActions.assertion(AccountPageObject.lblsavingcountbox$_sensa);
            await elementActions.assertion(AccountPageObject.lblsavingcountbox0_sensa);
            const availablemobilecoupons = (await AccountPageObject.lblavailableCoupons_sensa).getText();
            const mobilecoupons = (await availablemobilecoupons).replace(/[0-9]/g, '');
            console.log('Generated Text is: ', mobilecoupons);
            console.log('Expected Text is: ', availablecoupons);
            expect(mobilecoupons.trim()).toEqual(availablecoupons);

            console.log('Validated the Content in Coupons page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Coupons page', { error });
            throw error;
        }
    }

    async tobaccoPreferencesValidation(filename: string, sheetname: string, scenarioname: string, product: string) {
        try {
            const url = browser.getUrl();
            // #{products are "CIGARETTES", "VAPOR", "NICOTINE POUCHES", "MOIST SNUFF/DIP", "SNUS"}
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const productName = product.toUpperCase();
            const tellus = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltellus');
            await this.mssgcomparision(AccountPageObject.lbltellus_sensa, tellus);
            const typeofproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltypesofproducts');
            await this.mssgcomparision(AccountPageObject.lblproducttypes_sensa, typeofproduct);
            const cigarettCheckbox = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcigarett');
            await this.mssgcomparision(AccountPageObject.lblcigarettCheckbox, cigarettCheckbox);
            const vaporCheckbox = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvapor');
            await this.mssgcomparision(AccountPageObject.lblvaporCheckbox, vaporCheckbox);
            const nicotineCheckbox = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnicotine');
            await this.mssgcomparision(AccountPageObject.lblnicotineCheckbox, nicotineCheckbox);
            const moistCheckbox = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmoist');
            await this.mssgcomparision(AccountPageObject.lblmoistCheckbox, moistCheckbox);
            const snusCheckbox = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsnus');
            await this.mssgcomparision(AccountPageObject.lblsnusCheckbox, snusCheckbox);
            if (productName === 'CIGARETTES') {
                const hdrcigarett = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcigarette');
                await this.mssgcomparision(AccountPageObject.hdrcigarett_sensa, hdrcigarett);
                const cigaretteQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcigarettQ1');
                await this.mssgcomparision(AccountPageObject.lblcigarrettbrandquestion_sensa, cigaretteQ1);
                await elementActions.assertion(AccountPageObject.txtcigarrettbranddropdown_sensa);
                const cigaretteQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcigarettQ2');
                await this.mssgcomparision(AccountPageObject.lblcigarrettflavorquestion_sensa, cigaretteQ2);
                await elementActions.assertion(AccountPageObject.txtcigarrettflavordropdown_sensa);
                const cigaretteQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcigarettQ3');
                await this.mssgcomparision(AccountPageObject.lblcigarrettpurchasesquestion_sensa, cigaretteQ3);
                await elementActions.assertion(AccountPageObject.txtcigarrettpurchasesropdown_sensa);
            } else if (productName === 'VAPOR') {
                const hdrvapor = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrvapor');
                await this.mssgcomparision(AccountPageObject.hdrvapor_sensa, hdrvapor);
                const vaporQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvaporQ1');
                await this.mssgcomparision(AccountPageObject.lblvaporbrandquestion_sensa, vaporQ1);
                await elementActions.assertion(AccountPageObject.txtvaporbranddropdown_sensa);
                const vaporQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvaporQ2');
                await this.mssgcomparision(AccountPageObject.lblvaporflavorquestion_sensa, vaporQ2);
                await elementActions.assertion(AccountPageObject.txtvaporflavordropdown_sensa);
                const vaporQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvaporQ3');
                await this.mssgcomparision(AccountPageObject.lblvapornicotinepreferencequestion_sensa, vaporQ3);
                await elementActions.assertion(AccountPageObject.txtvapornicotinepreferencedropdown_sensa);
                const vaporQ4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblvaporQ4');
                await this.mssgcomparision(AccountPageObject.lblvaporpurchasesquestion_sensa, vaporQ4);
                await elementActions.assertion(AccountPageObject.txtvaporpurchasesropdown_sensa);
                await elementActions.assertion(AccountPageObject.lblicurrentlyVape_sensa);
            } else if (productName === 'NICOTINE POUCHES') {
                const hdrnicotine = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrnicotine');
                await this.mssgcomparision(AccountPageObject.hdrnicotine_sensa, hdrnicotine);
                const nicotineQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnicotineQ1');
                await this.mssgcomparision(AccountPageObject.lblnicotinebrandquestion_sensa, nicotineQ1);
                await elementActions.assertion(AccountPageObject.txtcigarrettbranddropdown_sensa);
                const nicotineQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnicotineQ2');
                await this.mssgcomparision(AccountPageObject.lblnicotineflavorquestion_sensa, nicotineQ2);
                await elementActions.assertion(AccountPageObject.txtcigarrettflavordropdown_sensa);
                const nicotineQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnicotineQ3');
                await this.mssgcomparision(AccountPageObject.lblnicotinepurchasesquestion_sensa, nicotineQ3);
                await elementActions.assertion(AccountPageObject.txtnicotinepurchasesropdown_sensa);
            } else if (productName === 'MOIST SNUFF/DIP') {
                const hdrmoist = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrmoist');
                await this.mssgcomparision(AccountPageObject.hdrmoist_sensa, hdrmoist);
                const moistQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmoistQ1');
                await this.mssgcomparision(AccountPageObject.lblmoistbrandquestion_sensa, moistQ1);
                await elementActions.assertion(AccountPageObject.txtmoistbranddropdown_sensa);
                const moistQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmoistQ2');
                await this.mssgcomparision(AccountPageObject.lblmoistflavorquestion_sensa, moistQ2);
                await elementActions.assertion(AccountPageObject.txtmoistflavordropdown_sensa);
                const moistQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmoistQ3');
                await this.mssgcomparision(AccountPageObject.lblmoistregularcutquestion_sensa, moistQ3);
                await elementActions.assertion(AccountPageObject.txtmoistregularcutdropdown_sensa);
                const moistQ4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmoistQ4');
                await this.mssgcomparision(AccountPageObject.lblmoistpurchasesquestion_sensa, moistQ4);
                await elementActions.assertion(AccountPageObject.txtmoistpurchasesropdown_sensa);
            } else if (productName === 'SNUS') {
                const hdrsnus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrsnus');
                await this.mssgcomparision(AccountPageObject.hdrsnus_sensa, hdrsnus);
                const snusQ1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsnusQ1');
                await this.mssgcomparision(AccountPageObject.lblsnusbrandquestion_sensa, snusQ1);
                await elementActions.assertion(AccountPageObject.txtsnusbranddropdown_sensa);
                const snusQ2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsnusQ2');
                await this.mssgcomparision(AccountPageObject.lblsnusflavorquestion_sensa, snusQ2);
                await elementActions.assertion(AccountPageObject.txtsnusflavordropdown_sensa);
                const snusQ3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsnusQ3');
                await this.mssgcomparision(AccountPageObject.lblsnuspurchasesquestion_sensa, snusQ3);
                await elementActions.assertion(AccountPageObject.txtsnuspurchasesropdown_sensa);
            }
            if ((await url).includes('registration')) {
                await elementActions.assertion(sensaRegistrationPageObject.btnsave_sensa);
            } else {
                await elementActions.assertion(sensaAccountPageObject.btnsurveyUpdate_sensa);
            }
            console.log('Validated the Content in Tobacco Preferences page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Tobacco Preferences page', { error });
            throw error;
        }
    }

    async logoutlinkfromAccountPage() {
        try {
            await this.clickonaccountlinkfromheader();
            await elementActions.waitForDisplayed(AccountPageObject.lnklogout_sensa);
            await elementActions.clickusingJavascript(AccountPageObject.lnklogout_sensa);
            await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
            console.log('User Logged out Successfully');
        } catch (error) {
            logger.error('Unable to Log out Successfully ', { error });
            throw error;
        }
    }

}
export default new AccountPage();
