class MyDevicePageObject {

    get lnkmydevice_sensa() { return $('//*[@title="My Device"]'); }
    get lblmydevicetitle_sensa() { return $('//h3[contains(@class,"cmp-teaser__title")]'); }
    get lblmydevicedescription_sensa() { return $('//*[contains(@class,"cmp-teaser__description ")]//p'); }
    get lbldevicedecode_sensa() { return $('//*[contains(text(),"device decode")]'); }
    get lbluserfriendly_sensa() { return $('//*[contains(text(),"User Friendly")]'); }
    get lbllink1_sensa() { return $('(//a[contains(text(),"Easy lock")])[2]'); }
    get lbllink2_sensa() { return $('(//a[contains(text(),"Flavor boost")])[2]'); }
    get lbllink3_sensa() { return $('(//a[contains(text(),"Clear view")])[2]'); }
    get lbllink4_sensa() { return $('//a[contains(text(),"Rechargeable")]'); }
    get lblvideo_sensa() { return $$('//*[@class="cmp-video-external__video-container"]'); }
    get lblvideo1_sensa() { return $('(//*[@class="cmp-video-external__video-container"])[2]'); }
    get lblvideo2_sensa() { return $('(//*[@class="cmp-video-external__video-container"])[4]'); }
    get lblvideo3_sensa() { return $('(//*[@class="cmp-video-external__video-container"])[6]'); }
    get lblvideo4_sensa() { return $('(//*[@class="cmp-video-external__video-container"])[8]'); }
    get lblcommonQuestion_sensa() { return $('//*[text()="Common Questions"]'); }
    get lblQ1_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[1]'); }
    get lblQ2_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[2]'); }
    get lblQ3_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[3]'); }
    get lblQ4_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[4]'); }
    get lblQ5_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[5]'); }
    get lblQ6_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[6]'); }
    get lblQ7_sensa() { return $('(//*[contains(@class,"cmp-accordion")]//span)[7]'); }
    get lblA11_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[1]'); }
    get lblA12_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[2]'); }
    get lblA21_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[3]'); }
    get lblA22_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[4]'); }
    get lblA23_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[5]'); }
    get lblA31_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[6]'); }
    get lblA32_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[7]'); }
    get lblA41_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[8]'); }
    get lblA51_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[9]'); }
    get lblA52_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[1]'); }
    get lblA53_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[2]'); }
    get lblA54_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[3]'); }
    get lblA55_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[4]'); }
    get lblA56_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[10]'); }
    get lblA57_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[11]'); }
    get lblA61_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[12]'); }
    get lblA62_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[13]'); }
    get lblA63_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[14]'); }
    get lblA64_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[15]'); }
    get lblA711_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[16]'); }
    get lblA712_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[5]'); }
    get lblA713_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[6]'); }
    get lblA714_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[7]'); }
    get lblA715_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[8]'); }
    get lblA716_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[17]'); }
    get lblA717_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[18]'); }
    get lblA718_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[19]'); }
    get lblA719_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[9]'); }
    get lblA720_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[10]'); }
    get lblA721_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[11]'); }
    get lblA722_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[12]'); }
    get lblA723_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[13]'); }
    get lblA724_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//li)[14]'); }
    get lblA725_sensa() { return $('(//*[contains(@class,"cmp-accordion-item__content")]//p)[20]'); }
}
export default new MyDevicePageObject();