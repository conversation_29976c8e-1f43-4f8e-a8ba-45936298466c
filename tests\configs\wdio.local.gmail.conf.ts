import type { Options } from '@wdio/types';
import { join } from 'path';
import path from 'path';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

export const config: WebdriverIO.Config & Options.Testrunner = {
    //
    // ====================
    // Runner Configuration
    // ====================
    runner: 'local',
    autoCompileOpts: {
        autoCompile: true,
        tsNodeOpts: {
            project: './tsconfig.json',
            transpileOnly: true,
        },
    },

    //
    // ==================
    // Specify Test Files
    // ==================
    specs: [
        join(process.cwd(), './tests/features/Gmail/**/*.feature'),
    ],

    exclude: [],

    //
    // ============
    // Capabilities
    // ============
    maxInstances: 1,
    capabilities: [{
        browserName: 'chrome',
        'goog:chromeOptions': {
            args: ['--headless', '--no-sandbox', '--disable-dev-shm-usage'],
        },
    }],

    //
    // ===================
    // Test Configurations
    // ===================
    logLevel: 'info',
    bail: 0,
    baseUrl: 'https://www.saucedemo.com',
    waitforTimeout: 10000,
    connectionRetryTimeout: 120000,
    connectionRetryCount: 3,

    //
    // ========
    // Services
    // ========
    services: [
        ['gmail', {
            credentials: path.join(process.cwd(), 'tests/resources/google-key/gmailCredentials.json'),
            token: path.join(process.cwd(), 'tests/resources/google-key/token.json'),
            intervalSec: 10,
            timeoutSec: 60,
        }],
    ],

    //
    // =========
    // Framework
    // =========
    framework: 'cucumber',

    //
    // =========
    // Reporters
    // =========
    reporters: [
        'spec',
        ['allure', {
            outputDir: 'reports/allure-results',
            disableWebdriverStepsReporting: true,
            disableWebdriverScreenshotsReporting: false,
        }],
    ],

    //
    // ===============
    // Cucumber Options
    // ===============
    cucumberOpts: {
        require: getStepDefinitionFiles(),
        backtrace: false,
        requireModule: [],
        dryRun: false,
        failFast: false,
        snippets: true,
        source: true,
        strict: false,
        tagExpression: '@GmailServiceSearch_Test',
        timeout: 60000,
        ignoreUndefinedDefinitions: false,
    },

    //
    // =====
    // Hooks
    // =====
    beforeSession: function (_config, _capabilities, _specs, _cid) {
        console.log('Starting Gmail Service Test Session...');
    },

    before: function (_capabilities, _specs) {
        console.log('Gmail Service Tests - Before Hook');
    },

    after: function (_result, _capabilities, _specs) {
        console.log('Gmail Service Tests - After Hook');
    },

    afterSession: function (_config, _capabilities, _specs) {
        console.log('Gmail Service Test Session Completed');
    },
};
