import { Then, When } from '@wdio/cucumber-framework';
import camelCouponsPage from '../../pages/camel/camel-coupons.page.ts';

When(/^The user clicks on Coupons Link$/, async function () {
    await camelCouponsPage.couponsPage();
});

Then(/^The user validates coupons page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelCouponsPage.couponsPageValidation(filepath, sheetname, scenarioname);
});

When(/^The user click on Redeem now button$/, async function () {
    await camelCouponsPage.redeemoffersbutton();
});