import { Then, When } from '@wdio/cucumber-framework';
import sensaFooterlinkPage from '../../pages/sensa/sensa-footerlink.page.ts';
import logger from '../../support/utils/logger.util.ts';
import camelFooterlinkPageObject from '../../page-object/camel/camel-footerlink.pageObject.ts';
import sensaFooterlinkPageObject from '../../page-object/sensa/sensa-footerlink.pageObject.ts';
const url = await browser.getUrl();


When(/^The user clicks on FAQ footerlink$/, async function () {
    await sensaFooterlinkPage.clickonfaqprefooterlink();
    if ((await url).includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrfaq_ContactUs_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa).toBeDisplayed();
    }
    logger.info("Navigated to FAQ Page Successfully");

});

Then(/^The user Validates FAQ Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.faqwarningPageValidation(filepath, sheetname, scenarioname);
    await sensaFooterlinkPage.faqProductPageValidation(filepath, sheetname, scenarioname);
    await sensaFooterlinkPage.faqwebsitePageValidation(filepath, sheetname, scenarioname);
    logger.info("Validated to FAQ Page Successfully");


});

When(/^The user clicks on Contact Us footerlink$/, async function () {
    await sensaFooterlinkPage.clickoncontactusprefooterlink();
    if ((await url).includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrfaq_ContactUs_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa).toBeDisplayed();
    }
    await logger.info('Navigated to Contact Us Page Successfully');
});

Then(/^The user Validates contact Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.contactusPageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to Contact Us Page Successfully');

});

When(/^The user clicks on Site Requirement footerlink$/, async function () {
    await sensaFooterlinkPage.clickonsiterquirementprefooterlink();
    if ((await url).includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrsitereq_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrsitereq_sensa).toBeDisplayed();
    }
    await logger.info('Navigated to  Site Requirement Successfully');
});

Then(/^The user Validates Site Requirement Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.siterequirementPageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to Site Requirement Page Successfully');
});

When(/^The user clicks on Terms Of Use footerlink$/, async function () {
    await sensaFooterlinkPage.clickontermsOfUseprefooterlink();
    await expect(sensaFooterlinkPageObject.hdrtermsOfUse_sensa).toBeDisplayed();
    await logger.info('Navigated to  Terms  Of Use Successfully');
});

Then(/^The user Validates Terms Of Use Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.termsofUsePageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to Terms Of Use Page Successfully');
});

When(/^The user clicks on Privacy Policy footerlink$/, async function () {
    await sensaFooterlinkPage.clickonprivacyPolicyprefooterlink();
    await expect(sensaFooterlinkPageObject.hdrprivacyPolicy_sensa).toBeDisplayed();
    await logger.info('Navigated to  Privacy Policy Successfully');
});

Then(/^The user Validates Privacy Policy Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.privacyPolicyPageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to Privacy Policy Page Successfully');
});
When(/^The user clicks on Text Messaging footerlink$/, async function () {
    await sensaFooterlinkPage.clickontextmessagingprefooterlink();
    if ((await url).includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrtextMessaging_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrtextMessaging_sensa).toBeDisplayed();
    }
    await logger.info('Navigated to  Text Messaging Successfully');
});

Then(/^The user Validates Text Messaging Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.textMessagingPageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to Text Messaging Page Successfully');
});


When(/^The user clicks on post login Contact Us footerlink$/, async function () {
    await sensaFooterlinkPage.clickoncontactuspostfooterlink();
    if (url.includes('sensa')) {
        await expect(sensaFooterlinkPageObject.hdrpostloginContactus_sensa).toBeDisplayed();
    } else {
        await expect(camelFooterlinkPageObject.hdrpostloginContactus_camel).toBeDisplayed();
        await logger.info('Navigated to post login Contact Us Successfully');
    }
});



Then(/^The user Validates post login contact Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.contactuspostloginPageValidation(filepath, sheetname, scenarioname);
    await logger.info('Validated to post login Contact Us Successfully');
});

When(/^The user clicks on post login FAQ footerlink$/, async function () {
    await sensaFooterlinkPage.clickonfaqpostfooterlink();
    await expect(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa).toBeDisplayed();
    await logger.info('Navigated to FAQ Page Successfully');
});

When(/^The user clicks on post login Site Requirements footerlink$/, async function () {
    await sensaFooterlinkPage.clickonsiterquirementpostfooterlink();
    if (url.includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrsitereq_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrsitereq_sensa).toBeDisplayed();
    }
    await logger.info('Navigated to post login Site Requirements Successfully');
});

When(/^The user clicks on post login Terms Of Use footerlink$/, async function () {
    await sensaFooterlinkPage.clickontermsOfUsepostfooterlink();
    await expect(sensaFooterlinkPageObject.hdrtermsOfUse_sensa).toBeDisplayed();
    await logger.info('Navigated to post login Terms Of Use Successfully');
});

When(/^The user clicks on post login Privacy Policy footerlink$/, async function () {
    await sensaFooterlinkPage.clickonprivacyPolicypostfooterlink();
    await expect(sensaFooterlinkPageObject.hdrprivacyPolicy_sensa).toBeDisplayed();
    await logger.info('Navigated to post login Privacy Policy Successfully');
});

When(/^The user clicks on post login Text Messaging footerlink$/, async function () {
    await sensaFooterlinkPage.clickontextmessagingpostfooterlink();

    if (url.includes('camel')) {
        await expect(camelFooterlinkPageObject.hdrtextMessaging_camel).toBeDisplayed();
    } else {
        await expect(sensaFooterlinkPageObject.hdrtextMessaging_sensa).toBeDisplayed();
    }
    await logger.info('Navigated to post login Text Messaging Successfully');
});