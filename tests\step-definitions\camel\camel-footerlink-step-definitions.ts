import { Then, When } from '@wdio/cucumber-framework';
import camelFooterlinkPage from '../../pages/camel/camel-footerlink.page.ts';
import sensaFooterlinkPage from '../../pages/sensa/sensa-footerlink.page.ts';


Then(/^The user Validates Camel FAQ Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelFooterlinkPage.faqPageValidation(filepath, sheetname, scenarioname);
  
});

When(/^The user clicks on Tobacco Rights footerlink$/, async function () {
    await sensaFooterlinkPage.clickontobaccorightsprefooterlink();
});

Then(/^The user Validates Tobacco Rights Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFooterlinkPage.tobaccoRightspreloginPageValidation(filepath, sheetname, scenarioname);
});


When(/^The user clicks on Camel Points footerlink$/, async function () {
    await camelFooterlinkPage.clickoncamelpointsprefooterlink();
});

Then(/^The user Validates Camel Pointss Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelFooterlinkPage.camelPOintsPageValidation(filepath, sheetname, scenarioname);
});

When(/^The user clicks on Sustainability footerlink$/, async function () {
    await camelFooterlinkPage.clickonsustainabilityprefooterlink();
});