import { browser } from '@wdio/globals';
import logger from  '../utils/logger.util.ts';
import { IWaitOptions, INavigationOptions } from '../types/action.types.ts';

class BrowserActions  {

    private readonly DEFAULT_TIMEOUT = 10000;

    /**
     * Navigates to a specific URL
     * @param url - The URL to navigate to
     * @param options - Navigation options like timeout and waitUntil condition
     */
    async navigateTo(url: string, options?: INavigationOptions): Promise<void> {
        try {
            logger.info(`Navigating to: ${url}`);
            await browser.url(url);
            if (options?.waitUntil) {
                await browser.waitUntil(options.waitUntil, {
                    timeout: options.timeout || 10000,
                    timeoutMsg: `Failed to load URL: ${url}`,
                });
            }
        } catch (error) {
            logger.error(`Failed to navigate to ${url}: ${error}`);
            throw error;
        }
    }

    /**
     * Waits for a condition to be true
     * @param condition - Function that returns a promise resolving to boolean
     * @param message - Error message if timeout occurs
     * @param options - Wait options like timeout and interval
     */
    async waitUntil(
        condition: () => Promise<boolean>,
        message: string,
        options?: IWaitOptions,
    ): Promise<void> {
        try {
            await browser.waitUntil(condition, {
                timeout: options?.timeout || 10000,
                interval: options?.interval || 500,
                timeoutMsg: message,
            });
        } catch (error) {
            logger.error(`Wait condition failed: ${message}`);
            throw error;
        }
    }

    /**
     * Switches to a specific window or tab
     * @param titleOrUrl - Window title or URL to switch to
     */
    async switchToWindow(titleOrUrl: string): Promise<void> {
        try {
            logger.info(`Switching to window: ${titleOrUrl}`);
            await browser.switchWindow(titleOrUrl);
        } catch (error) {
            logger.error(`Failed to switch to window ${titleOrUrl}: ${error}`);
            throw error;
        }
    }

    /**
     * Executes JavaScript in the browser context
     * @param script - Script to execute
     * @param args - Arguments to pass to the script
     */
    async executeScript<T>(
        script: string | ((...args: unknown[]) => T),
        ...args: unknown[]
    ): Promise<T> {
        try {
            return await browser.execute(script, ...args);
        } catch (error) {
            logger.error(`Script execution failed: ${error}`);
            throw error;
        }
    }

    /**
     * Takes a screenshot of the current page
     * @param filepath - Path to save the screenshot
     */
    async takeScreenshot(filepath?: string): Promise<void> {
        try {
            logger.info('Taking screenshot');
            await browser.saveScreenshot(
                filepath || `./screenshots/screenshot-${Date.now()}.png`,
            );
        } catch (error) {
            logger.error(`Screenshot capture failed: ${error}`);
            throw error;
        }
    }

    async waitForPageLoad(): Promise<void> {
        await browser.waitUntil(
            async () => {
                const state = await browser.execute('return document.readyState');
                return state === 'complete';
            },
            {
                timeout: this.DEFAULT_TIMEOUT,
                timeoutMsg: 'Page did not finish loading',
            },
        );
    }

    async getCurrentUrl(): Promise<string> {
        return await browser.getUrl();
    }

    async getTitle(): Promise<string> {
        return await browser.getTitle();
    }

    async refresh(): Promise<void> {
        await browser.refresh();
    }

    async setCookie(name: string, value: string): Promise<void> {
        await browser.setCookies([{
            name: name,
            value: value,
        }]);
    }

    async clearCookies(): Promise<void> {
        await browser.deleteCookies();
    }

}

export default new BrowserActions();
