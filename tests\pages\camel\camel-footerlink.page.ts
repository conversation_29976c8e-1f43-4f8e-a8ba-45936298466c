import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaFooterlinkPageObject from '../../page-object/sensa/sensa-footerlink.pageObject.ts';
import camelFooterlinkPageObject from '../../page-object/camel/camel-footerlink.pageObject.ts';
import sensaAccountPage from '../sensa/sensa-account.page.ts';
import sensaFooterlinkPage from '../sensa/sensa-footerlink.page.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';


class CamelFooterlinksPage {

    async faqPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblfurtherquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherquestion');
            const lblonthispage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblonthispage');
            const url = await browser.getUrl();
            if (url.includes('secure')) {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa, contactus);
            } else {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ContactUs_camel, contactus);
            }

            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfrequentQuestions, lblfurtherquestion);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblonthisPage_sensa, lblonthispage);
            const hdrlogin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwarnings');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion5');
            const hdroffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lbloffersandpromotions');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion6');
            const lblQuestion7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion7');
            const lblQuestion8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion8');
            const lblQuestion9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion9');
            const lblQuestion10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion10');
            const hdrageverification = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAge');
            const lblQuestion11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion11');
            const lblQuestion12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion12');
            const hdrprivacy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacy');
            const lblQuestion13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion13');
            const lblQuestion14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion14');
            const hdrtroubleshooting = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltroubleshooting');
            const lblQuestion15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion15');
            const lblQuestion16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion16');
            const lblQuestion17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion17');
            const hdrgeneral = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgeneral');
            const lblQuestion18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion18');
            const hdrenvironment = testData.getCellValue(SHEET_NAME, scenarioname, 'lblenvironment');
            const lblQuestion19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion19');
            const lblQuestion20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion20');
            const lblQuestion21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion21');
            const lblQuestion22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion22');
            const lblQuestion23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion23');
            const lblQuestion24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion24');

            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer6');
            const lblAnswer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer7');
            const lblAnswer8_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer81');
            const lblAnswer8_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer82');
            const lblAnswer9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9');
            const lblAnswer10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10');
            const lblAnswer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer11');
            const lblAnswer12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer12');
            const lblAnswer13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer13');
            const lblAnswer14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer14');
            const lblAnswer151 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer151');
            const lblAnswer152 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer152');
            const lblAnswer16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer16');
            const lblAnswer171 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer171');
            const lblAnswer172 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer172');
            const lblAnswer18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer18');
            const lblAnswer191 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer191');
            const lblAnswer192 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer192');
            const lblAnswer193 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer193');
            const lblAnswer20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer20');
            const lblAnswer211 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer211');
            const lblAnswer212 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer212');
            const lblAnswer221 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer221');
            const lblAnswer222 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer222');
            const lblAnswer231 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer231');
            const lblAnswer232 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer232');
            const lblAnswer24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer24');

            const lblneedfurther = testData.getCellValue(SHEET_NAME, scenarioname, 'lblneedfurther');
            const lblcontactprod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourcontact_prod');

            if (!url.includes('secure')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_loginandpassword_camel, hdrlogin);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ1_sensa, lblQuestion1);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA1_sensa, lblAnswer1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ2_sensa, lblQuestion2);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA2_sensa, lblAnswer2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ3_sensa, lblQuestion3);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA3_sensa, lblAnswer3);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ4_sensa, lblQuestion4);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA4_sensa, lblAnswer4);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ5_sensa, lblQuestion5);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA5_sensa, lblAnswer5);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_offersandpromotions_camel, hdroffers);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ6_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA6_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ7_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA7_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ8_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA8_sensa, lblAnswer8_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA82_sensa, lblAnswer8_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ9_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA92_sensa, lblAnswer9);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ10_sensa, lblQuestion10);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA93_sensa, lblAnswer10);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ageverification_camel, hdrageverification);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ11_sensa, lblQuestion11);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA94_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblProductQ1_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblProductQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA95_sensa, lblAnswer12);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_privacy_camel, hdrprivacy);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ2_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA96_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ3_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_1_sensa, lblAnswer14);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_troubleshoot_camel, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_2_sensa, lblAnswer151);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_2_sensa, lblAnswer152);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_4_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_sensa, lblAnswer171);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_3_sensa, lblAnswer172);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_general_camel, hdrgeneral);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA2_sensa, lblAnswer18);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_environment_camel, hdrenvironment);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer191);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer192);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer193);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer20);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ10_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA9_sensa, lblAnswer211);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer212);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ11_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA12_sensa, lblAnswer221);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer222);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ12_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ12_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA15_sensa, lblAnswer231);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA17_sensa, lblAnswer232);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ13_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ13_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA18_sensa, lblAnswer24);
            } else {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_offersandpromotions_camel, hdroffers);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ1_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA1_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ2_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA2_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ3_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA3_sensa, lblAnswer8_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA82_sensa, lblAnswer8_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ4_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA5_sensa, lblAnswer9);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ5_sensa, lblQuestion10);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA6_sensa, lblAnswer10);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ageverification_camel, hdrageverification);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ6_sensa, lblQuestion11);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA7_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ7_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA8_sensa, lblAnswer12);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_privacy_camel, hdrprivacy);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ8_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA91_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ9_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA92_sensa, lblAnswer14);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_troubleshoot_camel, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ10_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA93_sensa, lblAnswer151);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_2_sensa, lblAnswer152);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ11_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA95_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblProductQ1_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblProductQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA96_sensa, lblAnswer171);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_3_sensa, lblAnswer172);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_general_camel, hdrgeneral);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ2_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_2_sensa, lblAnswer18);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_environment_camel, hdrenvironment);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_4_sensa, lblAnswer191);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA1_sensa, lblAnswer192);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer193);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA4_sensa, lblAnswer20);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer211);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer212);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer221);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA10_sensa, lblAnswer222);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer231);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA13_sensa, lblAnswer232);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer24);

            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrneedfurther_sensa, lblneedfurther);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourcontact_sensa, lblcontactprod);
            await sensaFooterlinkPage.footerlinkoneachpage();
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }


    async clickoncamelpointsprefooterlink() {
        try {
            await elementActions.clickusingJavascript(camelLoginPageObject.lnkcamelpoits_camel);
            await sensaFooterlinkPage.allowpopup();
            await sensaFooterlinkPage.windowswitchandback();
            await elementActions.assertion(camelFooterlinkPageObject.hdrcamelPoints_camel);

            console.log('Navigated to Camel Points Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to  Camel Points Page', { error });
            throw error;
        }
    }

    async camelPOintsPageValidation(_filename: string, _sheetname: string, _scenarioname: string) {
        try {

            const SHEET_NAME = _sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', _filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, _scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrcamelPoints = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrcamelPoints');
            const lblcamelPointsterms = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsterms');
            const lblcamelPointsparara1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara1');
            const lblcamelPointsparara2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara2');
            await sensaAccountPage.mssgcomparisionremovelinespace(camelFooterlinkPageObject.hdrcamelPoints_camel, hdrcamelPoints);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsterms_camel, lblcamelPointsterms);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara1_camel, lblcamelPointsparara1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara2_camel, lblcamelPointsparara2);

            const lbleligibility = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibility');
            const lbljoiningpgr = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgr');
            const lblhowewecommunicate = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicate');
            const lblhowtoearn = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearn');
            const lblhowtoredeem = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeem');
            const lblhowtoavoid = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoid');
            const lblavailablerwards = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwards');
            const lblreserved = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserved');
            const lblprogram = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogram');
            const lbladditional = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditional');
            const lblclosing = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosing');
            const lbldisclaimer = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimer');
            const lblchanges = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchanges');
            const lblprivacy = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacy');
            const lblindemnity = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnity');
            const lblchoiceoflaw = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflaw')
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibility_camel, lbleligibility);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgr_camel, lbljoiningpgr);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicate_camel, lblhowewecommunicate);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearn_camel, lblhowtoearn);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeem_camel, lblhowtoredeem);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoid_camel, lblhowtoavoid);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwards_camel, lblavailablerwards);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserved_camel, lblreserved);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogram_camel, lblprogram);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditional_camel, lbladditional);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosing_camel, lblclosing);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimer_camel, lbldisclaimer);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchanges_camel, lblchanges);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacy_camel, lblprivacy);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnity_camel, lblindemnity);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflaw_camel, lblchoiceoflaw);

            const lbleligibilitydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc1');
            const lbleligibilitydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc2');
            const lbleligibilitydesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc3');
            const lbleligibilitydesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc4');
            const lbleligibilitydesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc5');
            const lbljoiningpgrdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc1');
            const lbljoiningpgrdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc2');
            const lbljoiningpgrdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc3');
            const lblhowewecommunicatedesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc1');
            const lblhowewecommunicatedesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc2');
            const lblhowewecommunicatedesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc3');
            const lblhowtoearndesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc1');
            const lblhowtoearndesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc2');
            const lblhowtoearndesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc3');
            const lblhowtoearndesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc4');
            const lblhowtoearndesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc5');
            const lblhowtoearndesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc6');
            const lblhowtoearndesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc7');
            const lblhowtoearndesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc8');
            const lblhowtoearndesc9 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc9');
            const lblhowtoearndesc10 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc10');
            const lblhowtoearndesc11 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc11');
            const lblhowtoearndesc12 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc12');
            const lblhowtoearndesc13 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc13');
            const lblhowtoredeemdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeemdesc1');
            const lblhowtoredeemdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeemdesc2');
            const lblhowtoavoiddesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc1');
            const lblhowtoavoiddesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc2');
            const lblhowtoavoiddesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc3');
            const lblavailablerwardsdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc1');
            const lblavailablerwardsdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc2');
            const lblavailablerwardsdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc3');
            const lblavailablerwardsdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc4');
            const lblavailablerwardsdesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc5');
            const lblavailablerwardsdesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc6');
            const lblavailablerwardsdesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc7');
            const lblreserveddesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserveddesc1');
            const lblreserveddesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserveddesc2');
            const lblprogramdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc1');
            const lblprogramdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc2');
            const lblprogramdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc3');
            const lblprogramdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc4');
            const lblprogramdesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc5');
            const lblprogramdesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc6');
            const lblprogramdesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc7');
            const lblprogramdesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc8');
            const lblprogramdesc9 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc9');
            const lblprogramdesc10 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc10');
            const lbladditionaldesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc1');
            const lbladditionaldesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc2');
            const lbladditionaldesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc3');
            const lbladditionaldesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc4');
            const lbladditionaldesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc5');
            const lbladditionaldesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc6');
            const lblclosingdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc1');
            const lblclosingdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc2');
            const lblclosingdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc3');
            const lblclosingdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc4');
            const lbldisclaimerdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc1');
            const lbldisclaimerdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc2');
            const lbldisclaimerdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc3');
            const lbldisclaimerdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc4');
            const lblchangesdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchangesdesc1');
            const lblchangesdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchangesdesc2');
            const lblprivacydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc1');
            const lblprivacydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc2');
            const lblprivacydesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc3');
            const lblprivacydesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc4');
            const lblprivacydesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc5');
            const lblprivacydesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc6');
            const lblprivacydesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc7');
            const lblprivacydesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc8');
            const lblindemnitydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnitydesc1');
            const lblindemnitydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnitydesc2');
            const lblchoiceoflawdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflawdesc1');
            const lblchoiceoflawdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflawdesc2');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc1_camel, lbleligibilitydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc2_camel, lbleligibilitydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc3_camel, lbleligibilitydesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc4_camel, lbleligibilitydesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc5_camel, lbleligibilitydesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc1_camel, lbljoiningpgrdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc2_camel, lbljoiningpgrdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc3_camel, lbljoiningpgrdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc1_camel, lblhowewecommunicatedesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc2_camel, lblhowewecommunicatedesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc3_camel, lblhowewecommunicatedesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc1_camel, lblhowtoearndesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc2_camel, lblhowtoearndesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc3_camel, lblhowtoearndesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc4_camel, lblhowtoearndesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc5_camel, lblhowtoearndesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc6_camel, lblhowtoearndesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc7_camel, lblhowtoearndesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc8_camel, lblhowtoearndesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc9_camel, lblhowtoearndesc9);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc10_camel, lblhowtoearndesc10);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc11_camel, lblhowtoearndesc11);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc12_camel, lblhowtoearndesc12);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc13_camel, lblhowtoearndesc13);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeemdesc1_camel, lblhowtoredeemdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeemdesc2_camel, lblhowtoredeemdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc1_camel, lblhowtoavoiddesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc2_camel, lblhowtoavoiddesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc3_camel, lblhowtoavoiddesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc1_camel, lblavailablerwardsdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc2_camel, lblavailablerwardsdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc3_camel, lblavailablerwardsdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc4_camel, lblavailablerwardsdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc5_camel, lblavailablerwardsdesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc6_camel, lblavailablerwardsdesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc7_camel, lblavailablerwardsdesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserveddesc1_camel, lblreserveddesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserveddesc2_camel, lblreserveddesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc1_camel, lblprogramdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc2_camel, lblprogramdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc3_camel, lblprogramdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc4_camel, lblprogramdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc5_camel, lblprogramdesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc6_camel, lblprogramdesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc7_camel, lblprogramdesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc8_camel, lblprogramdesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc9_camel, lblprogramdesc9);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc10_camel, lblprogramdesc10);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc1_camel, lbladditionaldesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc2_camel, lbladditionaldesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc3_camel, lbladditionaldesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc4_camel, lbladditionaldesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc5_camel, lbladditionaldesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc6_camel, lbladditionaldesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc1_camel, lblclosingdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc2_camel, lblclosingdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc3_camel, lblclosingdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc4_camel, lblclosingdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc1_camel, lbldisclaimerdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc2_camel, lbldisclaimerdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc3_camel, lbldisclaimerdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc4_camel, lbldisclaimerdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchangesdesc1_camel, lblchangesdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchangesdesc2_camel, lblchangesdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc1_camel, lblprivacydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc2_camel, lblprivacydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc3_camel, lblprivacydesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc4_camel, lblprivacydesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc5_camel, lblprivacydesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc6_camel, lblprivacydesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc7_camel, lblprivacydesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc8_camel, lblprivacydesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnitydesc1_camel, lblindemnitydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnitydesc2_camel, lblindemnitydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflawdesc1_camel, lblchoiceoflawdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflawdesc2_camel, lblchoiceoflawdesc2);
            await sensaFooterlinkPage.footerlinkoneachpage();
            console.log('Validated the Content in  Camel Points page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Camel Points page', { error });
            throw error;
        }
    }


    async clickonsustainabilityprefooterlink() {
        try {
            const url = await browser.getUrl();
            if(url.includes('aem')){
            await elementActions.clickusingJavascript(camelLoginPageObject.lnksustainability_camel);
            }else{
              await elementActions.clickusingJavascript(camelFooterlinkPageObject.lnksustainabilitypostlogin_camel);  
            }
            await sensaFooterlinkPage.allowpopup();
            await sensaFooterlinkPage.windowswitchandback();
              if(url.includes('aem')){
            await elementActions.clickElementIfExistsAndVisible(camelFooterlinkPageObject.btnacceptCookies_camel);
              }
            await elementActions.waitForDisplayed(camelFooterlinkPageObject.hdrsustainability_camel);
            await elementActions.assertion(camelFooterlinkPageObject.hdrsustainability_camel);
            console.log('Navigated to Sustainability Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Sustainability Page', { error });
            throw error;
        }
    }

}
export default new CamelFooterlinksPage();
