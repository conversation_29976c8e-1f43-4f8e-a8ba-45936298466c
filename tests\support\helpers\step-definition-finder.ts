import * as fs from 'fs';
import * as path from 'path';

/**
 * Recursively finds all TypeScript files in a directory
 * @param dir Directory to search
 * @param fileList Array to store found files
 * @returns Array of file paths
 */
function findTsFiles(dir: string, fileList: string[] = []): string[] {
  if (!fs.existsSync(dir)) {
    console.warn(`Directory does not exist: ${dir}`);
    return fileList;
  }

  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Recursively search subdirectories
      findTsFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      // Convert absolute path to relative path
      const relativePath = path.relative(process.cwd(), filePath)
        .replace(/\\/g, '/'); // Convert Windows backslashes to forward slashes

      fileList.push(`./${relativePath}`);
    }
  });

  return fileList;
}

/**
 * Gets all step definition files in the project
 * @returns Array of step definition file paths
 */
export function getStepDefinitionFiles(): string[] {
  const stepDefinitionsDir = path.join(process.cwd(), 'tests', 'step-definitions');
  console.log(`Searching for step definitions in: ${stepDefinitionsDir}`);

  const files = findTsFiles(stepDefinitionsDir);

  console.log(`Found ${files.length} step definition files:`);
  files.forEach(file => console.log(`  - ${file}`));

  return files;
}
