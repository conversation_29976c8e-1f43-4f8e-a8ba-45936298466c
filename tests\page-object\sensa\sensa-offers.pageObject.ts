class OffersPageObject {

    get hdroffers_sensa() { return $('//*[@title="Offers"]'); }
    get txtoffertitle_sensa() { return $('//*[@class="cmp-offers-mobile-only__greeting-text"]'); }
    get lbloffersAvailable_sensa() { return $('//*[@class="cmp-offers-mobile-only__offersAvailable active"]//h2'); }
    get imgoffers_sensa() { return $('//*[@class="cmp-offers-mobile-only__header-img default-offer-image "]'); }
    get lbloffersheadline_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__headline")]'); }
    get lblofferssubline_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__subtitle")]'); }
    get lblofferstep1_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-headline")])[1]'); }
    get lblofferstep2_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-headline")])[2]'); }
    get lblofferstep3_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-headline")])[3]'); }
    get lblofferstep4_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-headline")])[4]'); }
    get lblofferdesc1_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-description")])[1]//parent::*//p'); }
    get lblofferdesc2_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-description")])[2]//parent::*//p'); }
    get lblofferdesc3_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-description")])[3]//parent::*//p'); }
    get lblofferdesc4_sensa() { return $('(//*[contains(@class,"cmp-offers-mobile-only__step-description")])[4]//parent::*//p'); }
    get lblofferdisclaimer1_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[1]'); }
    get lblofferdisclaimer2_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[2]//b'); }
    get lblofferdisclaimer3_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[3]'); }
    get lblofferdisclaimer4_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[4]'); }
    get lblofferdisclaimer5_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[5]'); }
    get lblofferdisclaimer6_sensa() { return $('//*[contains(@class,"cmp-offers-mobile-only__disclaimers-text")]//parent::*//p[6]'); }
    get btnclaimoffers_sensa() { return $('//button[@type="submit"]//span[contains(text(),"Claim Mobile")]'); }
    get btnunderstood_sensa() { return $('//*[contains(text(),"UNDERSTOOD")]'); }
}
export default new OffersPageObject();