/**
 * Type declarations for wdio-gmail-service
 */

declare global {
    namespace WebdriverIO {
        interface Browser {
            /**
             * Check Gmail inbox for emails matching the given criteria
             * @param filters Email filter criteria
             * @returns Array of emails matching the criteria or null if none found
             */
            checkInbox(filters: {
                from?: string;
                to?: string;
                subject?: string;
                includeBody?: boolean;
                includeAttachments?: boolean;
                before?: Date;
                after?: Date;
                label?: string;
            }): Promise<Array<{
                from: string;
                to?: string;
                subject: string;
                date: string | Date;
                body?: {
                    html?: string;
                    text?: string;
                };
                attachments?: Array<{
                    filename: string;
                    content: string;
                    contentType: string;
                }>;
            }> | null>;
        }
    }
}

export {};
