import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import VeratedDataPageObject from '../../page-object/veratedData/veratedData.pageObject.ts';
import browserActions from '../../support/actions/browser.actions.ts';
import { faker } from '@faker-js/faker';


class VeratedData {
  // Initialize class properties with default empty values
  firstName: string = '';
  lastNAME: string = '';
  eMail: string = '';
  ssnNumber: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  zipCode: string = '';
  dateOfBirth: string = '';

  public async launchUrl(url: string) {
    try {
      await browserActions.navigateTo(url);
      await elementActions.waitForDisplayed(VeratedDataPageObject.addTestCaseButton);
      console.log(`Navigated to: ${url}`);
    } catch (error) {
      logger.error('Failed to Launch URL', { error });
      throw error;
    }
  }


  public async addTestCaseButton() {
    try {
      await elementActions.clickusingJavascript(VeratedDataPageObject.addTestCaseButton);
      await elementActions.waitForDisplayed(VeratedDataPageObject.submit);
      await elementActions.isDisplayed(VeratedDataPageObject.submit);
      console.log('Clicked Sucessfully on Add Test Case Button');
    } catch (error) {
      logger.error('Unable to click on Add Test Case Button', { error });
      throw error;
    }
  }


  public async VeratedDataDetails(addr: string, city: string, state: string, zipcode: string) {
    try {

      const firstname = faker.person.firstName();
      const lastnameElement = VeratedDataPageObject.lastName;
      const lastname = await lastnameElement.getAttribute('value');
      const randomAreaCode = Math.floor(100 + Math.random() * 900); // Generate a random area code (3 digits)
      const randomPrefix = Math.floor(100 + Math.random() * 900); // Generate a random prefix (3 digits)
      const randomLineNumber = Math.floor(1000 + Math.random() * 9000); // Generate a random line number (4 digits)
      const phonenumber = `(${randomAreaCode}) ${randomPrefix}-${randomLineNumber}`;
      const lastName = faker.person.lastName();
      const ssn = String(Math.floor(Math.random() * (9999 - 1001 + 1)) + 1001); // Generate a random number between 1001 and 999
      const email = `rjrautomationtest+${firstname}${ssn}${lastName}@gmail.com`;
      const ssnnumber = `386-76-${ssn}`;
      const randomYear = Math.floor(Math.random() * (2004 - 1998 + 1)) + 1998; // Generate a random year between 1950 and 2003
      const randomMonth = Math.floor(Math.random() * 12) + 1; // Generate a random month (1 to 12)
      const daysInMonth = new Date(randomYear, randomMonth, 0).getDate();
      const randomDay = Math.floor(Math.random() * daysInMonth) + 1; // Generate a random day based on the month
      //  Format the date as MM-DD-YYYY
      const dob = `${randomYear}-${String(randomMonth).padStart(2, '0')}-${String(randomDay).padStart(2, '0')}`;

      console.log('First Name', firstname);
      console.log('Last Name', lastname);
      console.log('Address', addr);
      console.log('City', city);
      console.log('State', state);
      console.log('Zipcode', zipcode);
      console.log('Email', email);
      console.log('SSN', ssn);
      console.log('Phone Number', phonenumber);
      console.log('Email', email);
      console.log('DOB', dob);
      await elementActions.setValue(VeratedDataPageObject.firstName, firstname);
      await elementActions.setValue(VeratedDataPageObject.address, addr);
      await elementActions.setValue(VeratedDataPageObject.city, city);
      await elementActions.setValue(VeratedDataPageObject.zipcode, zipcode);
      await elementActions.setValue(VeratedDataPageObject.phoneNumber, phonenumber);
      await elementActions.setValue(VeratedDataPageObject.email, email);
      await elementActions.setValue(VeratedDataPageObject.ssn, ssnnumber);
      await elementActions.click(VeratedDataPageObject.state);
      await elementActions.click(VeratedDataPageObject.getstatedd(state));
      const stateElement = VeratedDataPageObject.getstatedd(state);
      if (!(await stateElement.isDisplayed())) {
        await elementActions.click(VeratedDataPageObject.state);
      }
      await elementActions.click(VeratedDataPageObject.dateOfBirth);
      await elementActions.setValue(VeratedDataPageObject.dateOfBirth, dob);
      await elementActions.click(VeratedDataPageObject.submit);
      await elementActions.waitForDisplayed(VeratedDataPageObject.successMessage);
      await elementActions.isDisplayed(VeratedDataPageObject.successMessage);

      this.firstName = firstname;
      this.lastNAME = lastname;
      this.eMail = email;
      this.ssnNumber = ssn;
      this.address = addr;
      this.city = city;
      this.state = state;
      this.zipCode = zipcode;
      this.dateOfBirth = dob;

      console.log('Details are entered successfully');
    } catch (error) {
      logger.error('Unable to Enter Details', { error });
      throw error;
    }
  }



}
export default new VeratedData();
