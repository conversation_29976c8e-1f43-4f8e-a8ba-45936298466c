

class VeratedDataPageObject {

    get addTestCaseButton() { return $('//*[text()="Add Test Case"]'); }
    get firstName() { return $('input[name="fn"]'); }
    get lastName() { return $('input[name="ln"]'); }
    get address() { return $('input[name="addr"]'); }
    get city() { return $('input[name="city"]'); }
    get state() { return $('//*[text()="State"]/following-sibling::*/*/input'); }
    getstatedd(_state: string) { return $('//*[text()="State"]/following-sibling::*[2]/descendant::*[6]/div[text()="CA"]'); }
    get zipcode() { return $('input[name="zip"]'); }
    get phoneNumber() { return $('input[name="phone"]'); }
    get email() { return $('input[name="email"]'); }
    get ssn() { return $('input[name="ssn"]'); }
    get dateOfBirth() { return $('input[id="dob"]'); }
    get monthYear() { return $('div[class="react-datepicker__current-month"]'); }
    get previous() { return $('span[class="react-datepicker__navigation-icon react-datepicker__navigation-icon--previous"]'); }
    get submit() { return $('button[type="submit"]'); }
    get successMessage() { return $('div[class="status-message success"]'); }
    get lblFirstName() { return $('label[id="mantine-vble6w9m9-label"]'); }




}
export default new VeratedDataPageObject();