import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaMydevicePageObject from '../../page-object/sensa/sensa-mydevice.pageObject.ts';
import sensaAccountPage from './sensa-account.page.ts';
import sensaHomepagePageObject from '../../page-object/sensa/sensa-homepage.pageObject.ts';
import sensaOffersPageObject from '../../page-object/sensa/sensa-offers.pageObject.ts';
import sensaTextsignupPageObject from '../../page-object/sensa/sensa-textsignup.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import sensaFooterlinkPageObject from '../../page-object/sensa/sensa-footerlink.pageObject.ts';
import sensaFooterlinkPage from './sensa-footerlink.page.ts';

class HomePage {

    async hambergerMenu() {
        try {
            await elementActions.waitForDisplayed(sensaAccountPageObject.btnhamburgerMenu_sensa);
            await elementActions.clickusingJavascript(sensaAccountPageObject.btnhamburgerMenu_sensa);
            console.log('Clicked on Hameberger menu Successfully');
        } catch (error) {
            logger.error('Failed to Click on Hameberger menu', { error });
            throw error;
        }
    }

    async clickonflavorsfromheader() {
        try {
            await this.hambergerMenu();
             await elementActions.waitForDisplayed(sensaHomepagePageObject.lnkflavors_sensa);
            await elementActions.click(sensaHomepagePageObject.lnkflavors_sensa);
            await elementActions.assertion(sensaHomepagePageObject.hdrflavorspage_sensa);
            console.log('Navigated to Flavors Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Flavors ', { error });
            throw error;
        }
    }
    async storeLocatorPage() {
        try {
            await this.hambergerMenu();
            await elementActions.click(sensaHomepagePageObject.lnkstorelocator_sensa);
            await elementActions.assertion(sensaHomepagePageObject.hdrstorelocatorpage_sensa);
            console.log('Navigated to Store Locator Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Store Locator', { error });
            throw error;
        }
    }

    async homepagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            this.hambergerMenu();
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrflavors = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrflavors');
            const hdroffers = testData.getCellValue(SHEET_NAME, scenarioname, 'hdroffers');
            const hdrtextsignup = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrsignup');
            const hdrstorefinder = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrstorelocator');
            const hdrdevice = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldevice');
            const hdraccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblaccount');
            const hdraccount_prod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblaccount_Prod');
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkflavors_sensa, hdrflavors);
            await sensaAccountPage.mssgcomparision(sensaOffersPageObject.hdroffers_sensa, hdroffers);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaTextsignupPageObject.lnktextsignup_sensa, hdrtextsignup);
            }
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkstorelocator_sensa, hdrstorefinder);
            await sensaAccountPage.mssgcomparision(sensaMydevicePageObject.lnkmydevice_sensa, hdrdevice);
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lnkaccount_sensa, hdraccount);
            } else {
                await sensaAccountPage.mssgcomparision(sensaAccountPageObject.lnkaccountProd_sensa, hdraccount_prod);
            }
            await elementActions.click(sensaHomepagePageObject.lblhambergercloseicon_sensa);
            const hdrflavoryour = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrflavoryour');
            const lblsenses = testData.getCellValue(SHEET_NAME, scenarioname, 'lblletyoursenses');
            const lnkwatiszeronicotine = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkwatiszeronicotine');
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkwatiszeronicotine_sensa, lnkwatiszeronicotine);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblteaser_sensa, hdrflavoryour);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblteaserdesc_sensa, lblsenses);
            await elementActions.assertion(sensaHomepagePageObject.btnfindyourflavor_sensa);
            const lblberryfusion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblberryfusion');
            const lblberrywatermelon = testData.getCellValue(SHEET_NAME, scenarioname, 'lblberrywatermelon');
            const lblpassionfruit = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassionfruit');
            const lblmintfrost = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmintfrost');
            const lblwatermelonfrost = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwatermelonfrost');
            const lblblueberryfrost = testData.getCellValue(SHEET_NAME, scenarioname, 'lblblueberryfrost');
            await elementActions.assertion(sensaHomepagePageObject.imgberryfusion_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblberryfusion_sensa, lblberryfusion);
            await elementActions.assertion(sensaHomepagePageObject.imgberrywatermelonfusion_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblberrywatermelonfusion_sensa, lblberrywatermelon);
            await elementActions.assertion(sensaHomepagePageObject.imgpassionfrost_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblpassionfrost_sensa, lblpassionfruit);
            await elementActions.assertion(sensaHomepagePageObject.imgmintfrost_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblmintfrost_sensa, lblmintfrost);
            await elementActions.assertion(sensaHomepagePageObject.imgwatermelonfrost_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblwatermelonfrost_sensa, lblwatermelonfrost);
            await elementActions.assertion(sensaHomepagePageObject.imgblueberryfrost_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblblueberryfrost_sensa, lblblueberryfrost);
            const lblindulgein = testData.getCellValue(SHEET_NAME, scenarioname, 'lblindulgein');
            const lblindulgeinAndroid = testData.getCellValue(SHEET_NAME, scenarioname, 'lblindulgein_Android');
            const lblgettoknow = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgettoknow');
            if (driver.isIOS) {
                await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblindulgeinflavor_sensa, lblindulgein);
            } else {
                await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblindulgeinflavor_sensa, lblindulgeinAndroid);
            }
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblgettoknowzeronicotine_sensa, lblgettoknow);
            await elementActions.assertion(sensaHomepagePageObject.btndiscoversensa_sensa);
            const lblallflavors = testData.getCellValue(SHEET_NAME, scenarioname, 'lblallflavors');
            const lblzeronico = testData.getCellValue(SHEET_NAME, scenarioname, 'lblzeronicotine');
            const lblstorelocator = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstorelocator');
            const lblmobileoffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobileoffers');
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblallflavors_sensa, lblallflavors);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblzeronictine_sensa, lblzeronico);
            await elementActions.assertion(sensaHomepagePageObject.lblvideo_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblstorelocatortile_sensa, lblstorelocator);
            await elementActions.assertion(sensaHomepagePageObject.imgstorefinder_sensa);
            await elementActions.assertion(sensaHomepagePageObject.btnfindSensa_sensa);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lblclaimmobiletile_sensa, lblmobileoffers);
            await elementActions.assertion(sensaHomepagePageObject.imgclaimoffers_sensa);
            await elementActions.assertion(sensaHomepagePageObject.btnclaimoffers_sensa);
            const lnkoffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkoffers');
            const lnkproducts = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkproducts');
            const lnkaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkaccount');
            const lnkcontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkcontact');
            const lnkfaq = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkfaq');
            const lnksiterequiremtn = testData.getCellValue(SHEET_NAME, scenarioname, 'lnksiterequiremtn');
            const lnktermsofuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lnktermsofuse');
            const lnkprivacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkprivacypolicy');
            const lnktextmessaging = testData.getCellValue(SHEET_NAME, scenarioname, 'lnktextmessaging');
            const lnklogout = testData.getCellValue(SHEET_NAME, scenarioname, 'lnklogout');
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkoffers_sensa, lnkoffers);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkproducts_sensa, lnkproducts);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkmyAccount_sensa, lnkaccount);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkcontactus_sensa, lnkcontact);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkfaq_sensa, lnkfaq);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnksiterrequirements_sensa, lnksiterequiremtn);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnktermsofuse_sensa, lnktermsofuse);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnkprivacypolicy_sensa, lnkprivacypolicy);
            await sensaAccountPage.mssgcomparision(sensaHomepagePageObject.lnktextmessaging_sensa, lnktextmessaging);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lnklogout_sensa, lnklogout);
            console.log('Validated the Content in Home page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Home page', { error });
            throw error;
        }
    }

    async clickonallbuttonsandverifynavigationsinhomepage() {
        try {
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.btnfindyourflavor_sensa, sensaHomepagePageObject.hdrflavorspage_sensa);
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.btnfindSensa_sensa, sensaHomepagePageObject.hdrstorelocatorpage_sensa);
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.btnclaimoffers_sensa, sensaOffersPageObject.txtoffertitle_sensa);
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.lnkoffers_sensa, sensaOffersPageObject.txtoffertitle_sensa);
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.lnkmyAccount_sensa, sensaAccountPageObject.lblaccountheader_sensa);
            await this.clickOnEachButtonAndNaviagteBack(sensaHomepagePageObject.lnkproducts_sensa, sensaHomepagePageObject.hdrflavorspage_sensa);
            const initialHandles = await browser.getWindowHandle();
            console.log('Initial Window Handles:', initialHandles);
            await elementActions.click(sensaHomepagePageObject.lnkwatiszeronicotine_sensa);
            await driver.switchContext('NATIVE_APP');
            if (driver.isAndroid) {

                await driver.switchContext('CHROMIUM');
            } else {
                const locAllowPopUp = await sensaHomepagePageObject.lblallowpopup_sensa;
                const allowPopupDispalyed = await locAllowPopUp.isDisplayed();
                if (allowPopupDispalyed) {
                    await elementActions.click(locAllowPopUp);
                }
            }
            const contexts = await driver.getContexts();
            const webviewContext = contexts.find(ctx => typeof ctx === 'string' && ctx.startsWith('WEBVIEW'));
            if (webviewContext) {
                await driver.switchContext(webviewContext as string);
            }
            await sensaFooterlinkPage.windowswitchandback();
            await elementActions.assertion(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa);
            if (driver.isAndroid) {
                await browser.closeWindow();

            } else {
                await browser.execute(() => {
                    window.close();
                });
            }
            console.log('Verified Navigations Successfully');
        } catch (error) {
            logger.error('Failed to Click on Verify Navigations ', { error });
            throw error;
        }
    }

    async clickOnEachButtonAndNaviagteBack(element: ChainablePromiseElement, element1: ChainablePromiseElement) {
        try {
            await elementActions.waitForDisplayed(element);
            await elementActions.clickusingJavascript(element);
            await sensaFooterlinkPage.allowpopup();
             await elementActions.waitForDisplayed(element1);
            await elementActions.assertion(element1);
            await browser.execute(() => window.history.back());
            console.log('Navigated Back Successfully');
        } catch (error) {
            logger.error('Failed to navigate back', { error });
            throw error;
        }
    }


}
export default new HomePage();