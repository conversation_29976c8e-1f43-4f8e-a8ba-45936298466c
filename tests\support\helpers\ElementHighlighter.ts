/**
 * Highlighter utility for WebdriverIO that adds visible highlighting to elements
 * during test execution. This is useful for debugging and can be toggled on/off
 * via configuration.
 */
export class ElementHighlighter {
    private readonly enabled: boolean;
    private readonly highlightDuration: number;
    private readonly borderColor: string;
    private readonly borderWidth: string;
    private readonly backgroundColor: string;

    /**
     * Constructor for the ElementHighlighter class
     * @param config Configuration options for the highlighter
     */
    constructor(config: {
        enabled?: boolean;
        highlightDuration?: number;
        borderColor?: string;
        borderWidth?: string;
        backgroundColor?: string;
    } = {}) {
        this.enabled = config.enabled ?? true;
        this.highlightDuration = config.highlightDuration ?? 100;
        this.borderColor = config.borderColor ?? 'red';
        this.borderWidth = config.borderWidth ?? '3px';
        this.backgroundColor = config.backgroundColor ?? 'rgba(255, 0, 0, 0.2)';
    }

    /**
     * Highlights an element on the page
     * @param element WebdriverIO element to highlight
     * @returns The same element (for chaining)
     */
    async highlight<T extends WebdriverIO.Element>(element: T): Promise<T> {
        if (!this.enabled) {
            return element;
        }
        await element.scrollIntoView({
            behavior: 'auto',
            block: 'center',
            inline: 'center',
        });
        try {
            // Store the original styles to restore later
            const originalBorder = await browser.executeScript(
                'return arguments[0].style.border',
                [element],
            );

            const originalBackground = await browser.executeScript(
                'return arguments[0].style.backgroundColor',
                [element],
            );

            // Apply highlight styles
            await browser.executeScript(
                'arguments[0].style.border = arguments[1]; ' +
                'arguments[0].style.backgroundColor = arguments[2];',
                [element, `${this.borderWidth} solid ${this.borderColor}`, this.backgroundColor],
            );

            // Wait for the specified duration
            // eslint-disable-next-line wdio/no-pause
            await browser.pause(this.highlightDuration);

            // Restore original styles
            await browser.executeScript(
                'arguments[0].style.border = arguments[1]; ' +
                'arguments[0].style.backgroundColor = arguments[2];',
                [element, originalBorder, originalBackground],
            );
        } catch (error) {
            console.warn('Failed to highlight element:', error);
        }

        return element;
    }
}