Feature: Forgot Username Flow Validation in Camel Brand.

    Scenario Outline: Validate Forgot Username flow for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on Forgot Username Link
        Then The user validates Tell us about Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        Then The user validates Verify Identity Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters answer as <answer> for the security question
        Then The user validates Username Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The username <Username> is verified and Password <Password> is entered
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand

        @CamelForgotFlow_Validation_QA  
        Examples:
            | Brand | URL                         | Username                          | Password  | FirstName | LastName        | streetAddr      | zipCode | city     | state | dateOfBirth | answer | filename              | sheetname       | scenarioname                   |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | olive     | snvjSFVVqiGFXlU | 950 The Alameda | 95126   | San Jose | CA    | 01-01-2001  | school | aem-mobile-camel.json | Forgot Username | Validate Forgot Username Pages |

        @CamelForgotFlow_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                            | Password  | FirstName | LastName | streetAddr       | zipCode | city     | state | dateOfBirth | answer | filename              | sheetname       | scenarioname                   |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | LEONORA   | SPORER   | 514 MORRIS ST NE | 87123   | New York | NY    | 10-21-1998  | school | aem-mobile-camel.json | Forgot Username | Validate Forgot Username Pages |

    Scenario Outline: Validate user navigates to contact us page when entered wrong answer twice for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on Forgot Username Link
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        When The user enters wrong answer as <answer> for the security question and validates error Message <errormssg>
        Then The user reenters wrong answer as <answer> and navigates to contact us page

        @CamelStep2Error_Validation_QA 
        Examples:
            | Brand | URL                         | Username                          | Password  | FirstName | LastName        | streetAddr      | zipCode | city     | state | dateOfBirth | answer | errormssg                                                 |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | olive     | snvjSFVVqiGFXlU | 950 The Alameda | 95126   | San Jose | CA    | 01-01-2001  | Person | Your answer does not match our records. Please try again. |

        @CamelStep2Error_Validation_PROD  
        Examples:
            | Brand | URL                   | Username                            | Password  | FirstName | LastName | streetAddr       | zipCode | city     | state | dateOfBirth | answer | errormssg                                                 |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | LEONORA   | SPORER   | 514 MORRIS ST NE | 87123   | New York | NY    | 10-21-1998  | Person | Your answer does not match our records. Please try again. |


    Scenario Outline: Validate the Errors in all Pages user for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user clicks on Forgot Username Link
        And The user validates Errors in Tell us about Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <wrongstreetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        Then The user validates Generic Error in Tell us about Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user enters First Name as <FirstName> Last Name as <LastName> street address as <streetAddr> Zipcode as <zipCode> City as <city> State as <state> and DOB as "<dateOfBirth>"
        Then The user validates Errors in Verify Identity Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname> and enters correct answer as <answer>
        Then The user clicks on Reset Your Password link

        @CamelErrorPages_Validation_QA 
        Examples:
            | Brand | URL                         | Username                          | Password  | FirstName | LastName        | streetAddr      | zipCode | city     | state | dateOfBirth | wrongstreetAddr  | answer | filename              | sheetname       | scenarioname                   |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | olive     | snvjSFVVqiGFXlU | 950 The Alameda | 95126   | San Jose | CA    | 01-01-2001  | 514 MORRIS ST NE | school | aem-mobile-camel.json | Forgot Username | Validate Forgot Username Pages |

        @CamelErrorPages_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                            | Password  | FirstName | LastName | streetAddr       | zipCode | city     | state | dateOfBirth | wrongstreetAddr | answer | filename              | sheetname       | scenarioname                   |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | LEONORA   | SPORER   | 514 MORRIS ST NE | 87123   | New York | NY    | 10-21-1998  | 81 W 125TH ST   | school | aem-mobile-camel.json | Forgot Username | Validate Forgot Username Pages |
