Feature: Login Page Validation in Sensa Brand.

    Scenario Outline: Validate Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user Validates Signin Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        Then The user validates that successfully logged out

        @SensaLoginPage_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname | scenarioname          |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Login     | Login Page Validation |

        @SensaLoginPage_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname | scenarioname          |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Login     | Login Page Validation |

    Scenario Outline: Validate InValid Username and Password error in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with invalid user id <Username> or invalid password <invalidPassword>
        Then The user validates login error message <loginerror>
        When The user login with invalid user id <invalidUsername> or invalid password <Password>
        Then The user validates login error message <loginerror>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        Then The user validates that successfully logged out

        @SensaInValidData_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | invalidPassword | invalidUsername                      | loginerror                                                         |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

        @SensaInValidData_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  | invalidPassword | invalidUsername                       | loginerror                                                         |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

    Scenario Outline: Validate Rememeber Me Functionality in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user enters valid user id <Username> and password <Password>
        Then The user clicks on Remember me checkbox and log in
        Then The user should be able to login to the application successfully
        Then The user validates that successfully logged out
        Then The username <Username> should be displayed with remeber me checkbox selected

        @SensaRememberMe_Validation_QA  
        Examples:
            | Brand | URL                             | Username                           | Password  |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 |


        @SensaRememberMe_Validation_PROD 
        Examples:
            | Brand | URL                       | Username                             | Password  |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 |

