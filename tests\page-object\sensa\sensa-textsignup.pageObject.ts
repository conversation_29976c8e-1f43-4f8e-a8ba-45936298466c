class TextSignUpPageObject {

    get lnktextsignup_sensa() { return $('//*[@title="Text Sign-Up"]'); }
    get lblsignupTitle_sensa() { return $('//*[contains(@class,"cmp-teaser__title")][contains(text(),"Keep i")]'); }
    get lblsignupDescription_sensa() { return $('//*[contains(@class,"cmp-teaser__description")] //p'); }
    get lbltexttitle_sensa() { return $('//*[contains(@class,"cmp-title__text")]'); }
    get lblmobile_sensa() { return $('//*[contains(@class,"cmp-sms-opt-in-two__form-telephone-label")]'); }
    get txtmobilephone_sensa() { return $('//input[@id="telephone"]'); }
    get txtiCertify_sensa() { return $('//*[contains(@class,"cmp-form-options__field-description")]//p'); }
    get txtsubmit_sensa() { return $('//button[@type="submit"]'); }
    get lblvalidationError_sensa() { return $('//*[@class="validation error"]'); }
    get lblsuccessmssg_sensa() { return $('//*[@class="cmp-sms-opt-in-two__input-error submission-error error"]'); }
    get lblsuccesscheckyouphone_sensa() { return $('//*[@class="cmp-sms-opt-in-two__success-content"]//p'); }
    get lblsuccess_sensa() { return $('//*[@class="cmp-sms-opt-in-two__success-content"]//h4'); }


}
export default new TextSignUpPageObject();