import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import RegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import { expect } from '@wdio/globals';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaLoginPageObject from '../../page-object/sensa/sensa-login.pageObject.ts';
import sensaAccountPage from './sensa-account.page.ts';
import AccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import { error } from 'console';
import mobileActions from '../../support/actions/mobile.actions.ts';

class LoginPage {

    async loginPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const signin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsignin');
            const alreadyhave = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhaveanaccount');
            const forgot = testData.getCellValue(SHEET_NAME, scenarioname, 'lblforgot');
            const loginemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblloginemail');
            const loginremember = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrememberme');
            const loginpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
            const join = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoin');
            const donthaveaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnewaccount');
            const joinemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lbljoinemail');
            const legalnotice = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllegalNotice');
            const faq = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfaq');
            const siterequirement = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiterequirement');
            const privacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy');
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontactus');
            const termsofuse = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltermsuse');
            const textmessaging = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltextmessage');
            const copyright = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcopyright');
            await sensaAccountPage.mssgcomparision(RegistrationPageObject.lblsignIn_sensa, signin);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblalreadyhaveaccount_sensa, alreadyhave);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblforgot_sensa, forgot);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblloginemail_sensa, loginemail);
            await elementActions.assertion(AccountPageObject.txtusername_sensa);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblloginrememberme_sensa, loginremember);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblloginpassword_sensa, loginpassword);
            await elementActions.assertion(AccountPageObject.txtpassword_sensa);
            await elementActions.assertion(AccountPageObject.btnlogin_sensa);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lbljoin_sensa, join);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lbldonthaveaccount_sensa, donthaveaccount);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lbljoinemail_sensa, joinemail);
            await elementActions.assertion(RegistrationPageObject.txtregemail_sensa);
            await elementActions.assertion(RegistrationPageObject.txtjoinnow_sensa);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lbllegalnotice_sensa, legalnotice);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnkfaq_sensa, faq);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnksiteRequirement_sensa, siterequirement);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnkprivacyPolicy_sensa, privacypolicy);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnkcontactUs_sensa, contactus);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnktermsofUse_sensa, termsofuse);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lnktextMessaging_sensa, textmessaging);
            await sensaAccountPage.mssgcomparision(sensaLoginPageObject.lblcopyright_sensa, copyright);
            console.log('Validated the Content in Login page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Login page', { error });
            throw error;
        }
    }


    async loginwithinvalidcredential(username: string, password: string) {
        try { 
            await elementActions.waitForDisplayed(AccountPageObject.txtusername_sensa);
            await elementActions.setValue(AccountPageObject.txtusername_sensa, username);
            await elementActions.setValue(AccountPageObject.txtpassword_sensa, password);
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await elementActions.clickusingJavascript(AccountPageObject.btnlogin_sensa);
            } else {
                await elementActions.click(AccountPageObject.btnlogin_sensa);
            }
            await elementActions.assertion(RegistrationPageObject.lblsignIn_sensa);
            console.log('User should be Unable to Login  Successfully');
        } catch (error) {
            logger.error('Failed to Login', { error });
            throw error;
        }
    }

    async entercredentialinloginpage(username: string, password: string) {
        try {
             await elementActions.waitForDisplayed(AccountPageObject.txtusername_sensa);
            await elementActions.setValue(AccountPageObject.txtusername_sensa, username);
            await elementActions.setValue(AccountPageObject.txtpassword_sensa, password);
            console.log('Entered Credentials in Successfully');
        } catch (error) {
            logger.error('Failed to Enter Credentials', { error });
            throw error;
        }
    }


    async clickonremembermecheckboxandlogin() {
        try {
            const rememberme = await sensaLoginPageObject.txtremembermeCheckbox_sensa;
            if (driver.isIOS) {
                rememberme.click();
            } else {
                await elementActions.clickusingJavascript(rememberme);
            }
            const checkbox = await rememberme.isSelected();
            if (checkbox) {
                console.log('Remember me checkbox is selected Successfully');
            } else {
                logger.error('Failed to select Remember me checkbox', { error });
            }
            const url = browser.getUrl();
            if ((await url).includes('aem')) {
                await elementActions.clickusingJavascript(AccountPageObject.btnlogin_sensa);
            } else {
                await elementActions.click(AccountPageObject.btnlogin_sensa);
            }
            console.log('Logged in Successfully');
        } catch (error) {
            logger.error('Failed to Login', { error });
            throw error;
        }
    }


    async checkifusernameautopopuppostlogout(username: string) {
        try {
            const rememberme = await sensaLoginPageObject.txtremembermeCheckbox_sensa;
            const checkbox = await rememberme.isSelected();
            if (checkbox) {
                console.log('Remember me checkbox is selected Successfully');
            } else {
                logger.error('Failed to select Remember me checkbox', { error });
            }
            await elementActions.assertion(AccountPageObject.txtusername_sensa);
            const usernameValue = (await AccountPageObject.txtusername_sensa);
            const usernameText = await usernameValue.getValue();
             console.log('Expected Text is: ', usernameText);
            const actualsuccessmessage = (await usernameText).trim();
            console.log('Generated Text is: ', actualsuccessmessage);
            console.log('Expected Text is: ', username);
            expect(actualsuccessmessage).toEqual(username.trim());
            console.log('Username is displayed with remeber me checkbox is selected');
        } catch (error) {
            logger.error('Username is not displayed with remeber me checkbox is not selected', { error });
            throw error;
        }
    }
    async setLocation(lat: number, lon: number): Promise<void> {
        await mobileActions.setLocation(lat, lon);
  }
}
export default new LoginPage();