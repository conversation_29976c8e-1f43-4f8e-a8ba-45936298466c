// eslint.config.js
import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import * as wdioPlugin from 'eslint-plugin-wdio';

export default [
    {
        ignores: [
            'reports/**/*',
            'allure-results/**/*',
            'node_modules/**/*',
            'dist/**/*',
            'coverage/**/*'
        ]
    },
    js.configs.recommended,
    ...tseslint.configs.recommended,
    {
        files: ['**/*.ts'],
        languageOptions: {
            parserOptions: {
                project: './tsconfig.json',
                ecmaVersion: 2021,
                sourceType: 'module'
            },
            globals: {
                browser: 'readonly',
                $: 'readonly',
                $$: 'readonly',
                expect: 'readonly'
            }
        },
        plugins: {
            'wdio': wdioPlugin
        },
        rules: {
            // TypeScript specific rules
            'no-unused-vars': 'off',
            '@typescript-eslint/no-unused-vars': ['error', {
                'argsIgnorePattern': '^_',
                'varsIgnorePattern': '^_'
            }],
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/no-explicit-any': 'warn',

            // General code style
            'quotes': ['error', 'single'],
            'semi': ['error', 'always'],
            'comma-dangle': ['error', 'always-multiline'],
            'no-console': 'warn',

            // WebdriverIO specific rules
            'wdio/no-pause': 'error',
            'wdio/await-expect': 'error',
            'wdio/no-debug': 'warn',

            // Other useful rules
            'max-len': ['warn', { 'code': 100 }],
            'prefer-const': 'error',
            'no-var': 'error'
        }
    },
    {
        // Special rules for test files
        files: ['**/*.test.ts', '**/tests/**/*.ts', '**/step-definitions/**/*.ts'],
        rules: {
            'max-len': 'off',
            'no-console': 'off'
        }
    },
    {
        // Configuration for CommonJS files
        files: ['**/*.cjs'],
        languageOptions: {
            ecmaVersion: 2021,
            sourceType: 'script',
            globals: {
                require: 'readonly',
                module: 'readonly',
                exports: 'readonly',
                console: 'readonly',
                process: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                Buffer: 'readonly',
                global: 'readonly'
            }
        },
        rules: {
            // Disable TypeScript rules for CommonJS files
            '@typescript-eslint/no-require-imports': 'off',
            '@typescript-eslint/no-unused-vars': 'off',
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/no-explicit-any': 'off',

            // Enable standard JavaScript rules
            'no-unused-vars': ['error', {
                'argsIgnorePattern': '^_',
                'varsIgnorePattern': '^_'
            }],
            'quotes': ['error', 'single'],
            'semi': ['error', 'always'],
            'comma-dangle': ['error', 'always-multiline'],
            'no-console': 'off',
            'prefer-const': 'error',
            'no-var': 'error'
        }
    }
];