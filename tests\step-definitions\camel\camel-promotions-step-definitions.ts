import { When, Then } from '@wdio/cucumber-framework';
import camelPromotionsPage from '../../pages/camel/camel-promotions.page.ts';

When(/^The user clicks on Promotions$/, async function () {
    await camelPromotionsPage.promotionsPage();
});

Then(/^The user Validates Promotions Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelPromotionsPage.promotionsPageValidation(filepath, sheetname, scenarioname);
});

Then(/^The user enters valid comments as (.*) and verify last comment timestap as (.*)$/, async function (comment:string, timestamp:string) {
    await camelPromotionsPage.commentverification(comment,timestamp);
});

