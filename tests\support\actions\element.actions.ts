import logger from '../utils/logger.util.ts';
import { IWaitOptions } from '../types/action.types.ts';
import assertionHelper from '../helpers/assertion-helper.ts';

export class ElementActions {

    private readonly DEFAULT_TIMEOUT = 40000;
    private readonly DEFAULT_INTERVAL = 1000;
    /**
     * Clicks on an element
     * @param selector - Element selector
     * @param options - Action options like timeout
     */
    async click(element: ChainablePromiseElement): Promise<void> {
        try {
            logger.info('Clicking element');
            await element.highlight();
            await element.click();
        } catch (error) {
            logger.error(`Failed to click element: ${error}`);
            throw error;
        }
    }

    /**
     * Clicks on an element using JavaScript
     * Useful for elements that are difficult to click with regular click
     * @param element - Element to click
     */
    async clickusingJavascript(element: ChainablePromiseElement): Promise<void> {
        try {
            logger.info('Clicking element using JavaScript');
            await element.highlight();
            await this.waitForDisplayed(element);
            await browser.execute((el) => {
                el.click();
            }, await element);
        } catch (error) {
            logger.error(`Failed to click element using JavaScript: ${error}`);
            throw error;
        }
    }

    /**
     * Asserts that an element is displayed
     * Wrapper around assertionHelper.assertElementDisplayed
     * @param element - Element to assert
     * @param options - Wait options
     */
    async assertion(element: ChainablePromiseElement, options?: IWaitOptions): Promise<void> {
        try {
            logger.info('Asserting element is displayed');
             await this.waitForDisplayed(element);
            await assertionHelper.assertElementDisplayed(element, options);
        } catch (error) {
            logger.error(`Assertion failed: ${error}`);
            throw error;
        }
    }

    /**
     * Sets value for an input element
     * @param selector - Element selector
     * @param value - Value to set
     * @param options - Action options like timeout
     */
    async setValue(
        element: ChainablePromiseElement,
        value: string,
    ): Promise<void> {
        try {
            logger.info('Setting value for element');
            await element.highlight();
            await element.setValue(value);
        } catch (error) {
            logger.error(`Failed to set value for element : ${error}`);
            throw error;
        }
    }

    /**
     * Gets text from an element
     * @param element - Element selector
     */
    async getText(element: ChainablePromiseElement): Promise<string> {
        try {
            await element.highlight();
            return await element.getText();
        } catch (error) {
            logger.error(`Failed to get text from element: ${error}`);
            throw error;
        }
    }

    /**
     * Checks if an element is displayed
     * @param element - Element selector
     */
    async isDisplayed(element: ChainablePromiseElement): Promise<boolean> {
        await element.highlight();
        return await element.isDisplayed();
    }


    async waitForDisplayed(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
    ): Promise<void> {
        await element.waitForDisplayed({
            timeout: options?.timeout ?? this.DEFAULT_TIMEOUT,
            interval: options?.interval ?? this.DEFAULT_INTERVAL,
            reverse: options?.reverse ?? false,
            timeoutMsg: options?.timeoutMsg ??
                `Element not displayed after ${this.DEFAULT_TIMEOUT}ms`,
        });
    }

    async waitForClickable(element: ChainablePromiseElement, options?: IWaitOptions): Promise<void> {
        await element.waitForClickable({
            timeout: options?.timeout || this.DEFAULT_TIMEOUT,
            interval: options?.interval || this.DEFAULT_INTERVAL,
            reverse: options?.reverse || false,
            timeoutMsg: options?.timeoutMsg || `Element not clickable after ${this.DEFAULT_TIMEOUT}ms`,
        });
    }

    async waitForExist(element: ChainablePromiseElement, options?: IWaitOptions): Promise<void> {
        await element.waitForExist({
            timeout: options?.timeout || this.DEFAULT_TIMEOUT,
            interval: options?.interval || this.DEFAULT_INTERVAL,
            reverse: options?.reverse || false,
            timeoutMsg: options?.timeoutMsg || `Element does not exist after ${this.DEFAULT_TIMEOUT}ms`,
        });
    }

    async getAttribute(element: ChainablePromiseElement, attributeName: string): Promise<string> {
        await element.waitForExist({
            timeout: this.DEFAULT_TIMEOUT,
        });
        return await element.getAttribute(attributeName);
    }

    /**
    * Clicks an element if it exists and is clickable within the specified timeout.
    * Suppresses errors if the element is not found or not clickable.
    * Useful for optional elements like pop-up close buttons.
    * @param element - The ChainablePromiseElement to potentially click.
    * @param timeout - Maximum time in ms to wait for the element to be clickable. Defaults to 5000ms.
    */
    async clickElementIfExistsAndVisible(element: ChainablePromiseElement, timeout: number = 5000): Promise<void> {
        logger.debug(`Attempting to click optional element if clickable within ${timeout}ms.`);
        try {
            // Wait for the element to be clickable (implies exists, visible, enabled)
            // Use the provided timeout for this specific wait operation.
            await element.waitForClickable({
                timeout: timeout,
                timeoutMsg: `Optional element was not clickable within ${timeout}ms.`,
            });

            // If waitForClickable didn't throw, the element is ready.
            await element.highlight(); // Use existing highlight method
            await element.click();
            logger.info('Successfully clicked optional element.');

        } catch (error: unknown) {
            // Log that the element wasn't clickable within the timeout, but don't fail the test
            logger.info(`Skipping click on optional element. Reason: ${(error as Error).message}`);
        }
    }

    async highlightElement(element: ChainablePromiseElement): Promise<void> {
        await element.highlight();
    }
    async setValueUsingJavaScript(element: ChainablePromiseElement, value: string): Promise<void> {
        try {
            console.log('Using JavaScript method to set value');
            if (browser.isIOS) {
                await browser.execute('mobile: setValue', {
                    elementId: element.elementId,
                    text: value,
                });
                element.highlight();
            } else if (browser.isAndroid) {
                await browser.execute((el: HTMLInputElement, val: string) => {
                    el.value = val;
                    el.dispatchEvent(new Event('input', { bubbles: true }));
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }, element, value);
                element.highlight();
            } else {
                await element.setValue(value);
                element.highlight();
            }
        }
        catch (error) {
            logger.error(`Failed to set value using JavaScript: ${error}`);
            throw error;
        }
    };

}
export default new ElementActions();
