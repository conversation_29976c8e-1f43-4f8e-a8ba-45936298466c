class TextSignUpCamelPageObject {

     get hdrpromotions_camel() { return $('//*[@title="Promotions"]'); }
      get lblofficialrulesProd_camel() { return $('//*[@title="official rules"]'); }
     get lblNoThanks_camel() { return $('//a[normalize-space()="No Thanks"]'); }
     get hdrtextsignup_camel() { return $('//h1[normalize-space()="want all the updates?"]'); }
     get lbltextsignupdesc_camel() { return $('//p[contains(text(),"Sign up for texts from Camel and you")]'); }
     get lblentermobnum_camel() { return $('//h4[normalize-space()="Enter your mobile number to sign up"]'); }
     get imgpromotionsVideo_camel() { return $('//*[@class="cmp-video-external__overlay"]'); }
     get lblsuccess_camel() { return $('//*[@class="cmp-sms-opt-in-two__success-content"]'); }
     get hdrcomments_camel() { return $('//h1[normalize-space()="comments"]'); }
     get lblcommentsdesc_camel() { return $('//p[normalize-space()="ARE YOU READY?"]'); }
     get txtcommentbox_camel() { return $('//*[@class="cmp-comments__reply-comment-text"]'); }
     get btnshare_camel() { return $('//*[@class="cmp-comments__submit-reply"]'); }
     get lblcharactremaining_camel() { return $('//*[@class="cmp-comments__characters-remaining"]'); }
     get lblsubmissionguide_camel() { return $('//*[@class="cmp-comments__submission-guidelines"]'); }
     get lblcommenttime_camel() { return $('(//*[@class="cmp-comments__comment-timestamp"])[1]'); }
     get lblcommentmssg_camel() { return $('(//*[@class="cmp-comments__comment-content"])[1]'); }


}
export default new TextSignUpCamelPageObject();