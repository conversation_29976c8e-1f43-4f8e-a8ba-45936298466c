import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaSgwPageObject from '../../page-object/sensa/sensa-sgw.pageObject.ts';
import logger from '../../support/utils/logger.util.ts';
import GoogleVisionUtil from '../../support/utils/googleVisionUtil.ts';
import elementActions from '../../support/actions/element.actions.ts';


class SGW {

    async getQuarterMonth(month: number) {

        if (month >= 1 && month <= 3) {
            return 'Q1';
        } else if (month >= 4 && month <= 6) {
            return 'Q2';
        } else if (month >= 7 && month <= 9) {
            return 'Q3';
        } else if (month >= 10 && month <= 12) {
            return 'Q4';
        } else {
            return 'Invalid month';
        }
    }

    async getQuarterMonthProd(month: number) {

        if (month >= 1 && month <= 3) {
            return 'Q1';
        } else if (month >= 4 && month <= 6) {
            return 'Q2';
        } else if (month >= 7 && month <= 9) {
            return 'Q3';
        } else if (month >= 10 && month <= 12) {
            return 'Q4';
        } else {
            return 'Invalid month';
        }
    }

    async compareSGWText(filename: string, sheetname: string, scenarioname: string) {
        try {
            const currentDate = new Date();
            const month = currentDate.getMonth();
            const url = browser.getUrl();
            let quarter = '';
            if ((await url).includes('aem')) {
                quarter = await this.getQuarterMonth(month);
            } else {
                quarter = await this.getQuarterMonthProd(month);
            }

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const Q1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ1');
            const Q2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ2');
            const Q3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ3');
            const Q4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQ4');
            const visionUtil = new GoogleVisionUtil();
            let sgwMessage = '';
            if ((await url).includes('sensavape')) {
                await elementActions.highlightElement(sensaSgwPageObject.lblSGW_sensa);
                sgwMessage = await visionUtil.detectTextFromElementSingleLine(sensaSgwPageObject.lblSGW_sensa);
            } else {
                const sgw = await sensaSgwPageObject.lblSGW_camel;
                let sgwMessages ;
                for (const sgwWarning of sgw) {
                    if (await sgwWarning.isDisplayed() && await sgwWarning.isClickable()) {
                       sgwMessages = sgwWarning;
                       break;
                    }
                }
                        if(sgwMessages){
                        await elementActions.highlightElement(sgwMessages);
                        sgwMessage = await visionUtil.detectTextFromElementSingleLine(sgwMessages);
}
                    
            }
            if (await quarter == 'Q1') {
                await this.sgwTextcomparision(sgwMessage, Q1);
                console.log('Validated SGW Message:', Q1);
            }
            else if (await quarter == 'Q2') {
                await this.sgwTextcomparision(sgwMessage, Q2);
                console.log('Validated SGW Message:', Q2);
            }
            else if (await quarter == 'Q3') {
                await this.sgwTextcomparision(sgwMessage, Q3);
                console.log('Validated SGW Message:', Q3);
            }
            else if (await quarter == 'Q4') {
                await this.sgwTextcomparision(sgwMessage, Q4);
                console.log('Validated SGW Message:', Q4);
            } else {
                logger.error('Invalid Quarter');
            }

        } catch (error) {
            logger.error('Unable to Validate SGW Message', { error });
            throw error;
        }
    }

    async sgwTextcomparision(actualsuccessmessage: string, mssg: string) {
        try {
            console.log('Generated Text is: ', actualsuccessmessage);
            console.log('Expected Text is: ', mssg);
            expect(actualsuccessmessage).toEqual(mssg.trim());
            console.log('Validated Message Successfully');
        } catch (error) {
            logger.error('Failed to Validate Message', { error });
            throw error;
        }
    }


}
export default new SGW();