import { Then, When } from '@wdio/cucumber-framework';
import sensaFlavorPage from '../../pages/sensa/sensa-flavor.page.ts';
import logger from '../../support/utils/logger.util.ts';



Then(/^The user validate the Flavors page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaFlavorPage.validateflavorPage(filepath, sheetname, scenarioname);
     logger.info('Validated Flavors Page Successfully');
   

});