{"name": "aem-mobilesite-webdriverio-cucumber", "version": "1.0.0", "description": "This is a WebdriverIO-based test automation framework that leverages TypeScript and Cucumber for behavior-driven testing. It is designed for testing web, mobile, and hybrid applications on Sauce Labs.", "repository": {"type": "git", "url": "https://batdigital.visualstudio.com/DBS%20Digital%20Marketing/_git/WebDriverIO_AEM-MobileSites_Automation"}, "license": "ISC", "author": "", "type": "module", "main": "eslint.config.js", "directories": {"test": "tests"}, "scripts": {"lint": "eslint .", "lint:wdio-cucumber": "eslint --config eslint.cucumber.config.js .", "sauce-visual": "wdio run ./tests/configs/wdio.saucelabs.desktop.conf.ts --spec tests/features/inventory.feature", "sauce-visual-mobile": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/inventory.feature", "sauce-visual-check": "VISUAL_CHECK=true wdio run ./tests/configs/wdio.saucelabs.desktop.conf.ts --spec tests/features/inventory.feature", "sauce-visual-check-mobile": "VISUAL_CHECK=true wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/inventory.feature", "wdio": "wdio run ./wdio.conf.ts", "clean:reports": "rimraf allure-results/*reports/*", "allure:generate": "allure generate allure-results --clean -o allure-report", "allure:open": "allure serve allure-results", "report": "npm run allure:generate && npm run allure:open", "test-gmail": "wdio run ./tests/configs/wdio.local.gmail.conf.ts --spec tests/features/Gmail/Gmail-Service-Test.feature", "test-sensa-account": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/sensa/mobile-accountpage.feature", "test-sensa-qavalidation": "wdio run ./tests/configs/wdio.saucelabs.mobile-SensaQA.conf.ts", "test-sensa-footerlink": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/camel/mobile-footerlinkpage.feature", "test-sensa-store": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/sensa/mobile-forgotpasswordpage.feature --retry 2", "test-sensa-store1": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts"}, "dependencies": {"@types/webdriverio": "^5.0.0", "allure": "^0.0.0", "appium": "^2.19.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@faker-js/faker": "^9.8.0", "@google-cloud/vision": "^5.2.0", "@saucelabs/wdio-sauce-visual-service": "^0.13.0", "@types/axios": "^0.14.4", "@types/chai": "^5.2.2", "@types/mssql": "^9.1.7", "@types/readline-sync": "^1.4.8", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@wdio/allure-reporter": "^9.16.2", "@wdio/appium-service": "^9.16.2", "@wdio/cli": "^9.16.2", "@wdio/cucumber-framework": "^9.16.2", "@wdio/devtools-service": "^8.42.0", "@wdio/globals": "^9.16.2", "@wdio/json-reporter": "^9.16.2", "@wdio/junit-reporter": "^9.16.2", "@wdio/local-runner": "^9.16.2", "@wdio/logger": "^9.16.2", "@wdio/mocha-framework": "^9.16.2", "@wdio/protocols": "^9.16.2", "@wdio/runner": "^9.16.2", "@wdio/sauce-service": "^9.16.2", "@wdio/spec-reporter": "^9.16.2", "allure-commandline": "^2.34.1", "axios": "^1.10.0", "chai": "^5.2.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "eslint": "^9.29.0", "eslint-plugin-cucumber": "^2.0.0", "eslint-plugin-wdio": "^9.16.2", "exceljs": "^4.4.0", "gmail-tester": "^1.3.8", "mssql": "^11.0.1", "readline-sync": "^1.4.10", "rimraf": "^6.0.1", "rm": "^0.1.8", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "update-dotenv": "^1.1.1", "wdio-gmail-service": "^2.1.0", "wdio-html-nice-reporter": "^8.1.7", "wdio-wait-for": "^3.1.0", "webdriverio": "^9.16.2", "winston": "^3.17.0"}, "engines": {"node": "^16.13 || >=18"}}