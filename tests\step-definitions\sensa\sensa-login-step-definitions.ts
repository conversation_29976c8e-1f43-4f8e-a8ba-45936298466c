import { Then, When } from '@wdio/cucumber-framework';
import LoginPage from '../../pages/sensa/sensa-login.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import sensaLoginPageObject from '../../page-object/sensa/sensa-login.pageObject.ts';

Then(/^The user Validates Signin Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await LoginPage.loginPageValidation(filepath, sheetname, scenarioname);
    logger.info("Validated Login Page Successfully");
});

When(/^The user login with invalid user id (.*) or invalid password (.*)$/, async function (username: string, password: string) {
    await LoginPage.loginwithinvalidcredential(username, password);
    await expect(sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info("User should be Unable to Login  Successfully");

});

When(/^The user enters valid user id (.*) and password (.*)$/, async function (username: string, password: string) {
    await LoginPage.entercredentialinloginpage(username, password);
    await expect(sensaAccountPageObject.txtusername_sensa).toHaveValue(username);
     await expect(sensaAccountPageObject.txtpassword_sensa).toHaveValue(password);
       logger.info('Entered Credentials in Successfully');
});

Then(/^The user clicks on Remember me checkbox and log in$/, async function () {
    await LoginPage.clickonremembermecheckboxandlogin();
     logger.info('User should login Successfully');
});

Then(/^The username (.*) should be displayed with remeber me checkbox selected$/, async function (username: string) {
    await LoginPage.checkifusernameautopopuppostlogout(username);
    await expect(await sensaLoginPageObject.txtremembermeCheckbox_sensa).toBeSelected();
    await expect(sensaAccountPageObject.txtusername_sensa).toHaveText(username);
     logger.info('Username is displayed with remeber me checkbox is selected');

});

