import logger from '../utils/logger.util.ts';
import { IWaitOptions } from '../types/action.types.ts';

// Define more specific types for our assertion functions
type AssertionFunction<T> = (...args: unknown[]) => Promise<T>;

/**
 * Comprehensive assertion utilities for WebdriverIO tests
 */
class AssertionHelper {
    // Default wait options that can be reused across methods
    private readonly DEFAULT_TIMEOUT = 10000;
    private readonly DEFAULT_INTERVAL = 500;

    /**
     * Creates default wait options with the provided overrides
     */
    private createWaitOptions(options?: Partial<IWaitOptions>, timeoutMsg?: string): IWaitOptions {
        return {
            timeout: options?.timeout || this.DEFAULT_TIMEOUT,
            interval: options?.interval || this.DEFAULT_INTERVAL,
            timeoutMsg: options?.timeoutMsg || timeoutMsg || `Operation timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
            error: options?.error,
            reverse: options?.reverse || false,
        };
    }

    /**
     * Helper method to highlight an element or scroll it into view
     * - Attempts to highlight the element if highlighting is enabled
     * - Falls back to scrollIntoView with block center when highlighting is disabled or fails
     */
    private async highlightOrScroll(element: ChainablePromiseElement): Promise<void> {
        // Default scroll options for consistency
        const scrollOptions = { block: 'center', inline: 'center', behavior: 'smooth' as const };

        // Check if highlighting is enabled via the environment variable
        const isHighlightEnabled = browser.highlightElements === true;

        if (isHighlightEnabled) {
            try {
                // Attempt to highlight the element
                await element.highlight();
                // If highlighting succeeds, we're done
                return;
            } catch (error) {
                // Log the error but continue with scrollIntoView as fallback
                logger.debug('Element highlighting failed, falling back to scrollIntoView', { error });
            }
        }

        // Either highlighting is disabled or it failed, so use scrollIntoView as fallback
        try {
            await element.scrollIntoView(scrollOptions);
        } catch (error) {
            // Log but don't throw - scrolling is a best-effort operation
            logger.debug('Failed to scroll element into view', { error });
        }
    }

    /**
     * Wrapper to standardize error handling and logging for assertion methods
     */
    private async assertWrapper<T>(
        fn: AssertionFunction<T>,
        successMessage: string,
        errorMessage: string,
        ...args: unknown[]
    ): Promise<T> {
        try {
            const result = await fn(...args);
            logger.info(`Assertion passed: ${successMessage}`);
            return result;
        } catch (error) {
            logger.error(`${errorMessage}`, { error });
            throw error;
        }
    }

    /**
     * Helper to wait for an element to meet a condition
     */
    private async waitForElementCondition(
        element: ChainablePromiseElement,
        condition: () => Promise<boolean>,
        waitOptions: IWaitOptions,
        message: string,
    ): Promise<void> {
        await element.waitForDisplayed(waitOptions);
        await this.highlightOrScroll(element);

        await browser.waitUntil(condition, {
            timeout: waitOptions.timeout,
            interval: waitOptions.interval,
            timeoutMsg: message,
        });
    }

    //====================================================================================
    // Element State Assertions
    //====================================================================================

    /**
     * Asserts that an element is displayed
     */
    async assertElementDisplayed(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await element.waitForDisplayed(waitOptions);
                await this.highlightOrScroll(element);

            },
            'Element is displayed',
            'Failed to assert element is displayed',
            element, options, message,
        );
    }

    /**
     * Asserts that an element exists in the DOM (but might not be visible)
     */
    async assertElementExists(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should exist but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await element.waitForExist(waitOptions);
                await this.highlightOrScroll(element);

            },
            'Element exists in DOM',
            'Failed to assert element exists',
            element, options, message,
        );
    }

    /**
     * Asserts that an element is NOT displayed
     */
    async assertElementNotDisplayed(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    { ...options, reverse: true },
                    `Element should not be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await element.waitForDisplayed(waitOptions);

            },
            'Element is not displayed',
            'Failed to assert element is not displayed',
            element, options, message,
        );
    }

    /**
     * Asserts that an element is enabled
     */
    async assertElementEnabled(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => await element.isEnabled(),
                    waitOptions,
                    message || `Element should be enabled but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            'Element is enabled',
            'Failed to assert element is enabled',
            element, options, message,
        );
    }

    /**
     * Asserts that an element is disabled
     */
    async assertElementDisabled(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => !(await element.isEnabled()),
                    waitOptions,
                    message || `Element should be disabled but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            'Element is disabled',
            'Failed to assert element is disabled',
            element, options, message,
        );
    }

    /**
     * Asserts that a checkbox or radio button is selected
     */
    async assertElementSelected(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => await element.isSelected(),
                    waitOptions,
                    message || `Element should be selected but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            'Element is selected',
            'Failed to assert element is selected',
            element, options, message,
        );
    }

    /**
     * Asserts that a checkbox or radio button is NOT selected
     */
    async assertElementNotSelected(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => !(await element.isSelected()),
                    waitOptions,
                    message || `Element should not be selected but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            'Element is not selected',
            'Failed to assert element is not selected',
            element, options, message,
        );
    }

    //====================================================================================
    // Text Content Assertions
    //====================================================================================

    /**
     * Asserts that an element's text matches exactly the expected text
     */
    async assertElementText(
        element: ChainablePromiseElement,
        expectedText: string,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => {
                        const actualText = (await element.getText()).trim();
                        return actualText === expectedText.trim();
                    },
                    waitOptions,
                    message || `Element text should be "${expectedText}" but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            `Element text matches "${expectedText}"`,
            'Failed to assert element text',
            element, expectedText, options, message,
        );
    }

    /**
     * Asserts that an element's text contains the expected text
     */
    async assertElementContainsText(
        element: ChainablePromiseElement,
        partialText: string,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => {
                        const actualText = (await element.getText()).trim();
                        return actualText.includes(partialText);
                    },
                    waitOptions,
                    message || `Element text should contain "${partialText}" but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
            },
            `Element text contains "${partialText}"`,
            'Failed to assert element contains text',
            element, partialText, options, message,
        );
    }

    /**
     * Asserts that an element's text matches a regular expression
     */
    async assertElementTextMatches(
        element: ChainablePromiseElement,
        regex: RegExp,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await this.waitForElementCondition(
                    element,
                    async () => {
                        const actualText = await element.getText();
                        return regex.test(actualText);
                    },
                    waitOptions,
                    message || `Element text should match pattern "${regex}" but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
                const actualText = await element.getText();
                await expect(actualText).toMatch(regex);
            },
            `Element text matches pattern "${regex}"`,
            'Failed to assert element text matches pattern',
            element, regex, options, message,
        );
    }

    /**
     * Asserts that there is at least one element
     */
    async assertElementsExist(
        elements: ChainablePromiseArray,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = {
                    timeout: options?.timeout || this.DEFAULT_TIMEOUT,
                    interval: options?.interval || this.DEFAULT_INTERVAL,
                    timeoutMsg: options?.timeoutMsg || 'Expected at least one element, but found none',
                };

                await browser.waitUntil(
                    async () => (await elements).length > 0,
                    waitOptions,
                );
                const elementArray = await elements;
                await expect(elementArray).toBeElementsArrayOfSize({ gte: 1 });
            },
            'Found at least one element',
            'Failed to assert elements exist',
            elements, options, message,
        );
    }

    //====================================================================================
    // Visual/UI Assertions
    //====================================================================================

    /**
     * Asserts that an element is in the viewport
     */
    async assertElementInViewport(
        element: ChainablePromiseElement,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await element.waitForExist({ timeout: this.DEFAULT_TIMEOUT });
                await this.highlightOrScroll(element);
                await expect(element).toBeDisplayedInViewport();
            },
            'Element is in viewport',
            'Failed to assert element in viewport',
            element, message,
        );
    }

    //====================================================================================
    // URL/Navigation Assertions
    //====================================================================================

    /**
     * Asserts that the current URL matches exactly
     */
    async assertUrl(expectedUrl: string, message?: string): Promise<void> {
        return this.assertWrapper(
            async () => {
                await browser.waitUntil(
                    async () => (await browser.getUrl()) === expectedUrl,
                    {
                        timeout: this.DEFAULT_TIMEOUT,
                        timeoutMsg: `URL should be "${expectedUrl}"`,
                    },
                );
            },
            `URL is "${expectedUrl}"`,
            'Failed to assert URL',
            expectedUrl, message,
        );
    }

    /**
     * Asserts that the current URL contains a specific string
     */
    async assertUrlContains(partialUrl: string, message?: string): Promise<void> {
        return this.assertWrapper(
            async () => {
                await browser.waitUntil(
                    async () => (await browser.getUrl()).includes(partialUrl),
                    {
                        timeout: this.DEFAULT_TIMEOUT,
                        timeoutMsg: `URL should contain "${partialUrl}"`,
                    },
                );
            },
            `URL contains "${partialUrl}"`,
            'Failed to assert URL contains',
            partialUrl, message,
        );
    }

    /**
     * Asserts that the page title matches exactly
     */
    async assertTitle(expectedTitle: string, message?: string): Promise<void> {
        return this.assertWrapper(
            async () => {
                await browser.waitUntil(
                    async () => (await browser.getTitle()) === expectedTitle,
                    {
                        timeout: this.DEFAULT_TIMEOUT,
                        timeoutMsg: `Title should be "${expectedTitle}"`,
                    },
                );
            },
            `Title is "${expectedTitle}"`,
            'Failed to assert title',
            expectedTitle, message,
        );
    }

    //====================================================================================
    // Value Assertions
    //====================================================================================

    /**
     * Asserts that a dropdown has a specific selected option
     */
    async assertDropdownSelectedValue(
        element: ChainablePromiseElement,
        expectedText: string,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await element.waitForDisplayed({ timeout: this.DEFAULT_TIMEOUT });
                const selectedOption = await element.$('option:checked');
                const selectedText = await selectedOption.getText();
                await expect(selectedText.trim()).toEqual(expectedText.trim());
            },
            `Dropdown has "${expectedText}" selected`,
            'Failed to assert dropdown selected value',
            element, expectedText, message,
        );
    }

    /**
     * Asserts that a dropdown contains a specific option
     */
    async assertDropdownHasOption(
        element: ChainablePromiseElement,
        optionText: string,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await element.waitForDisplayed({ timeout: this.DEFAULT_TIMEOUT });
                const options = await element.$$('option');

                let optionExists = false;
                for (const option of options) {
                    const text = await option.getText();
                    if (text.trim() === optionText.trim()) {
                        optionExists = true;
                        break;
                    }
                }
                await expect(optionExists).toBe(true);
            },
            `Dropdown contains option "${optionText}"`,
            'Failed to assert dropdown has option',
            element, optionText, message,
        );
    }

    //====================================================================================
    // Error/Success Message Assertions
    //====================================================================================

    /**
     * Asserts that an error message is displayed with specific text
     */
    async assertErrorMessage(
        element: ChainablePromiseElement,
        expectedText: string,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await element.waitForDisplayed({ timeout: this.DEFAULT_TIMEOUT });
                await this.highlightOrScroll(element);

                await browser.waitUntil(
                    async () => {
                        const actualText = (await element.getText()).trim();
                        return actualText === expectedText.trim();
                    },
                    {
                        timeout: this.DEFAULT_TIMEOUT,
                        timeoutMsg: message || `Error message should be "${expectedText}" but timed out after ${this.DEFAULT_TIMEOUT}ms`,
                    },
                );
            },
            `Error message is "${expectedText}"`,
            'Failed to assert error message',
            element, expectedText, message,
        );
    }

    /**
     * Asserts that a success message appears and then disappears after a delay
     */
    async assertSuccessMessageAppearsThenDisappears(
        element: ChainablePromiseElement,
        expectedText: string,
        disappearTimeoutMs: number = 10000,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const appearWaitOptions = this.createWaitOptions(
                    options,
                    `Success message should appear but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await element.waitForDisplayed(appearWaitOptions);
                await this.highlightOrScroll(element);

                await this.waitForElementCondition(
                    element,
                    async () => {
                        const actualText = (await element.getText()).trim();
                        return actualText === expectedText.trim();
                    },
                    appearWaitOptions,
                    message || `Success message should be "${expectedText}" but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
                const disappearWaitOptions = this.createWaitOptions(
                    {
                        timeout: disappearTimeoutMs,
                        interval: options?.interval || this.DEFAULT_INTERVAL,
                        reverse: true,
                    },
                    `Success message should disappear but was still visible after ${disappearTimeoutMs}ms`,
                );

                await element.waitForDisplayed(disappearWaitOptions);
            },
            `Success message "${expectedText}" appeared and then disappeared`,
            'Failed to assert success message appears then disappears',
            element, expectedText, disappearTimeoutMs, options, message,
        );
    }

    /**
     * Assert a toast notification appears with expected message
     */
    async assertToastNotification(
        toastSelector: string,
        expectedText: string,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                // Use $ without await since it returns a ChainablePromiseElement
                const toast = $(toastSelector);

                const waitOptions = this.createWaitOptions(
                    options,
                    `Toast notification should appear but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );

                await toast.waitForDisplayed(waitOptions);
                await this.highlightOrScroll(toast);

                await browser.waitUntil(
                    async () => {
                        const actualText = (await toast.getText()).trim();
                        return actualText.includes(expectedText.trim());
                    },
                    {
                        timeout: options?.timeout || this.DEFAULT_TIMEOUT,
                        interval: options?.interval || this.DEFAULT_INTERVAL,
                        timeoutMsg: message || `Toast should contain "${expectedText}" but timed out waiting after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                    },
                );
            },
            `Toast notification contains "${expectedText}"`,
            `Failed to assert toast notification: Toast should contain "${expectedText}"`,
            toastSelector, expectedText, options, message,
        );
    }

    //====================================================================================
    // Form Assertions
    //====================================================================================

    /**
     * Asserts that a form is in a valid state (all fields valid, no error messages)
     */
    async assertFormValid(
        formElement: ChainablePromiseElement,
        errorSelector: string,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await formElement.waitForExist({ timeout: this.DEFAULT_TIMEOUT });
                await this.highlightOrScroll(formElement);
                const errorElements = await formElement.$$(errorSelector);
                const visibleErrorElements = [];

                for (const errorElement of errorElements) {
                    if (await errorElement.isDisplayed()) {
                        visibleErrorElements.push(await errorElement.getText());
                    }
                }
                await expect(visibleErrorElements.length).toBe(0);
            },
            'Form is valid with no visible errors',
            'Failed to assert form validity',
            formElement, errorSelector, message,
        );
    }

    /**
     * Asserts that required form fields are marked as required
     */
    async assertRequiredFields(
        formElement: ChainablePromiseElement,
        requiredFieldSelectors: string[],
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                await formElement.waitForExist({ timeout: this.DEFAULT_TIMEOUT });
                await this.highlightOrScroll(formElement);

                for (const selector of requiredFieldSelectors) {
                    const field = await formElement.$(selector);
                    await field.waitForExist({ timeout: 5000 });
                    const isRequiredAttr = await field.getAttribute('required');
                    const isAriaRequired = await field.getAttribute('aria-required');

                    const isRequired = isRequiredAttr === 'true' || isRequiredAttr === '' || isAriaRequired === 'true';
                    await expect(isRequired).toBe(true);
                }
            },
            'All specified fields are marked as required',
            'Failed to assert required fields',
            formElement, requiredFieldSelectors, message,
        );
    }
}

export default new AssertionHelper();