Feature: Flavors page Validation for Sensa Brand.

    Scenario Outline: Sensa Flavors page Validation for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        And The user click on Flavours link
        Then The user validate the Flavors page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user validates that successfully logged out

        @Sensa_flavorpage_Validation_QA
        Examples:
            | Brand | URL                             | Username                           | Password  | filename              | sheetname | scenarioname          |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Flavor    | Flavor Page Validation |

        @Sensa_flavorpage_Validation_PROD
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname | scenarioname          |
            | Sensa | https://www.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Flavor    | Flavor Page Validation |