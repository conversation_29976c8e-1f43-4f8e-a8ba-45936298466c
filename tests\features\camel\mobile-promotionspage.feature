Feature: Promotions Page Validation in Camel Website.

    Scenario Outline: Vaidate Promotions Page  for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Promotions
        Then The user Validates Promotions Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters valid Mobile Number as <mobileno> and validates success message as <succesmmsg>
        Then The user enters valid comments as <comments> and verify last comment timestap as <timestamp>
         Then The user validates that successfully logged out of camel brand

        @CamelValidatePromotions_Validation_QA 
        Examples:
            | Brand | URL                         | Username                           | Password  | mobileno     | succesmmsg                                                        | comments | timestamp   | filename              | sheetname  | scenarioname             |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | ************ | THANK YOU! Look for a text message to complete your registration. | Testing  | 0 Hours Ago | aem-mobile-camel.json | Promotions | Validate Promotions Page |

    Scenario Outline: Vaidate Error Message when entered invalid Mobile Number in Promotions Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Promotions
        Then The user enters invalid Mobile Number as <mobileno> and validates error message as <error>
         Then The user validates that successfully logged out of camel brand

        @CamelInValidMobileNumber_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | mobileno     | error                                                       |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | ************ | Please provide a valid phone number in xxx-xxx-xxxx format. |

# @CamelInValidMobileNumber_Validation_Prod
# Examples:
#     | Brand | URL                   | Username                             | Password  | mobileno     | error                                                       |
#     | Camel | https://www.camel.com | <EMAIL> | Password1 | ************ | Please provide a valid phone number in xxx-xxx-xxxx format. |

