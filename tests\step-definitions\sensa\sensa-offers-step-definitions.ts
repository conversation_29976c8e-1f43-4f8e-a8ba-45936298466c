import { Then, When } from '@wdio/cucumber-framework';
import sensaOffersPage from '../../pages/sensa/sensa-offers.page.ts';

When(/^The user clicks on Offers Link$/, async function () {
    await sensaOffersPage.clickonofferslink();
});

Then(/^The user validates offers page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaOffersPage.offersPageValidation(filepath, sheetname, scenarioname);
});

When(/^The user click on Claim Mobile Offers button$/, async function () {
    await sensaOffersPage.clickonclaimoffersbuttonandvalidatenavigationtospa();
});