Feature: Footerlink Pages Validation in Camel Brand.

    Scenario Outline: Vaidate FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on FAQ footerlink
        Then The user Validates Camel FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginFAQFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname            |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @CamelPreLoginFAQFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname            |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Contact us footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Contact Us footerlink
        Then The user Validates contact <PERSON> with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginContactUsFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname            |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate FAQ footerlink |

        @CamelPreLoginContactUsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname            |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate FAQ footerlink |

    Scenario Outline: Vaidate Site Requirement footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Site Requirement footerlink
        Then The user Validates Site Requirement Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginSiteRequirementFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                         |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

        @CamelPreLoginSiteRequirementFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname                         |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate SiteRequirements footerlink |

    Scenario Outline: Vaidate Terms Of Use footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Terms Of Use footerlink
        Then The user Validates Terms Of Use Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginTermsOfUseFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                     |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

        @CamelPreLoginTermsOfUseFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname                     |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Terms of Use footerlink |

    Scenario Outline: Vaidate Privacy Policy footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Privacy Policy footerlink
        Then The user Validates Privacy Policy Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginPrivacyPolicyFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                       |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

        @CamelPreLoginPrivacyPolicyFooterlink_Validation_PROD
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                       |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Privacy Policy footerlink |

    Scenario Outline: Vaidate Text Messaging footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Text Messaging footerlink
        Then The user Validates Text Messaging Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginTextMessagingFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                       |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Text Messaging footerlink |

        @CamelPreLoginTextMessagingFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname                       |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Text Messaging footerlink |

    Scenario Outline: Vaidate Tobacco Rights footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Tobacco Rights footerlink
        Then The user Validates Tobacco Rights Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginTobaccoRightsFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                       |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Tobacco Rights footerlink |

        @CamelPreLoginTobaccoRightsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname                       |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Tobacco Rights footerlink |

    Scenario Outline: Vaidate Camel Points footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Camel Points footerlink
        Then The user Validates Camel Pointss Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPreLoginCamelPointsFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | filename              | sheetname             | scenarioname                     |
            | Camel | https://aem-stage.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Camel Points footerlink |

        @CamelPreLoginCamelPointsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | filename              | sheetname             | scenarioname                     |
            | Camel | https://www.camel.com | aem-mobile-camel.json | Pre-login Footerlinks | Validate Camel Points footerlink |


    Scenario Outline: Vaidate Camel Points footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user clicks on Sustainability footerlink

        @CamelPreLoginSustainabilityFooterlink_Validation_QA 
        Examples:
            | Brand | URL                         |
            | Camel | https://aem-stage.camel.com |

        @CamelPreLoginSustainabilityFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                   |
            | Camel | https://www.camel.com |


    Scenario Outline: Vaidate Contact us footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login Contact Us footerlink
        Then The user Validates post login contact Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginContactUsFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                               |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate post login contact us  footerlink |

        @CamelPostLoginContactUsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                               |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate post login contact us  footerlink |

    Scenario Outline: Vaidate FAQ footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login FAQ footerlink
        Then The user Validates Camel FAQ Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginFAQFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname            |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate FAQ footerlink |

        @CamelPostLoginFAQFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname            |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate FAQ footerlink |


    Scenario Outline: Vaidate Tobacco Rights footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Tobacco Rights footerlink
        Then The user Validates Tobacco Rights Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginTobaccoRightsFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                       |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Tobacco Rights footerlink |

        @CamelPostLoginTobaccoRightsFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                       |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Tobacco Rights footerlink |


    Scenario Outline: Vaidate Site Requirements footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login Site Requirements footerlink
        Then The user Validates Site Requirement Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginSiteRequirementFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                         |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate SiteRequirements footerlink |

        @CamelPostLoginSiteRequirementFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                         |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate SiteRequirements footerlink |

    Scenario Outline: Vaidate Terms Of Use footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login Terms Of Use footerlink
        Then The user Validates Terms Of Use Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginTermsOfUseFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                                |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Terms of Use post login footerlink |

        @CamelPostLoginTermsOfUseFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                                |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Terms of Use post login footerlink |


    Scenario Outline: Vaidate Privacy Policy footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login Privacy Policy footerlink
        Then The user Validates Privacy Policy Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginPrivacyPolicyFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                                   |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Privacy Policy  post login footerlink |

        @CamelPostLoginPrivacyPolicyFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                                   |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Privacy Policy  post login footerlink |


    Scenario Outline: Vaidate Text Messaging footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on post login Text Messaging footerlink
        Then The user Validates Text Messaging Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginextMessagingFooterlink_Validation_QA
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                                   |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Text Messaging  post login footerlink |

        @CamelPostLoginextMessagingFooterlink_Validation_PROD
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                                   |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Text Messaging  post login footerlink |

    Scenario Outline: Vaidate Camel Points footerlink page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        When The user clicks on Camel Points footerlink
        Then The user Validates Camel Pointss Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>

        @CamelPostLoginCamelPointsFooterlink_Validation_QA 
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname              | scenarioname                     |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Camel Points footerlink |

        @CamelPostLoginCamelPointsFooterlink_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname              | scenarioname                     |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Post-login Footerlinks | Validate Camel Points footerlink |
