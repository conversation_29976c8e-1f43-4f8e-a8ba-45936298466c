import { Then, When } from '@wdio/cucumber-framework';
import camelHomePage from '../../pages/camel/camel-home.page.ts';

Then(/^The user Validates Camel Homepage Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelHomePage.homePageValidation(filepath, sheetname, scenarioname);
    await camelHomePage.fedbannerValidation(filepath, sheetname, scenarioname);
});



When(/^The user clicks on Products$/, async function () {
    await camelHomePage.navigatetoProductspage();
});
