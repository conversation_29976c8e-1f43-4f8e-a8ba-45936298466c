import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*.*',
      'appium:autoAcceptAlerts': false,
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        build,
        appiumVersion: 'latest',
      },
      webSocketUrl: false,
    },
    // {
    //   // iPhone 15 - Another latest real iOS device
    //   'appium:deviceName': 'iPhone 15',
    //   'appium:automationName': 'XCUITest',
    //   browserName: 'Safari',
    //   platformName: 'iOS',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //   },
    // },
    //  {
    //   // Samsung Galaxy S24 Ultra - Latest real Android device

    //   'appium:deviceName': 'Samsung.*',
    //   'appium:automationName': 'UiAutomator2',
    //   'appium:platformVersion': '15',
    //   browserName: 'Chrome',
    //   platformName: 'Android',
    //   'sauce:options': {
    //     appiumVersion: 'stable',
    //     build,

    //   },
    //    // Chrome-specific capabilities at the root level
    //    'goog:chromeOptions': {
    //     args: [
    //         '--start-maximized',
    //         '--disable-notifications',
    //         '--disable-popup-blocking'
    //     ]
    // },
    //   webSocketUrl: false,
    //   'appium:resetKeyboard':true,
    //   'appium:unicodeKeyboard':true
    // },
    // {
    //   // Google Pixel 8 Pro - Latest real Android device
    //   "appium:deviceName": "Google.*",
    //   'appium:platformVersion': '15',
    //   "appium:automationName": "UiAutomator2",
    //   browserName: "Chrome",
    //   platformName: "Android",
    //   "sauce:options": {
    //     appiumVersion: "latest",
    //     build,
    //   },
    //   webSocketUrl: false,
    //   unhandledPromptBehavior:'dismiss'
    // },
  ],

  specs: [join(process.cwd(), './tests/features/**/*.feature')],

  // Add these important configurations
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
    ['gmail', {
      credentials: join(process.cwd(), 'tests/resources/google-key/gmailCredentials.json'),
      token: join(process.cwd(), 'tests/resources/google-key/token.json'),
      intervalSec: 10,
      timeoutSec: 60,
    }],
  ],
  maxInstances: 4,
  waitforTimeout: 50000,
  connectionRetryTimeout: 150000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay:0,
  specFileRetriesDeferred: false,


  // Cucumber specific configuration
  cucumberOpts: {
    require: getStepDefinitionFiles(),
    backtrace: false,
    requireModule: [],
    dryRun: false,
    failFast: false,
    snippets: true,
    source: true,
    strict: false,
    tags: ' @SensaContactInfo_Update_PROD  or @SensaSecurityPage_Update_PROD or @SensaTobaccoPreferences_Update_PROD  or @SensaRetaileAccount_Update_PROD  or @SensaCouponsPage_Validation_PROD  or @Sensa_flavorpage_Validation_PROD or  @SensaPreLoginFAQFooterlink_Validation_PROD  or  @SensaPreLoginContactUsFooterlink_Validation_PROD  or @SensaPreLoginSiteRequirementFooterlink_Validation_PROD or @SensaPreLoginTermsOfUseFooterlink_Validation_PROD or  @SensaPreLoginPrivacyPolicyFooterlink_Validation_PROD  or  @SensaPreLoginTextMessagingFooterlink_Validation_PROD  or @SensaPostLoginContactUsFooterlink_Validation_PROD  or  @SensaPostLoginFAQFooterlink_Validation_PROD  or  @SensaPostLoginSiteRequirementFooterlink_Validation_PROD or @SensaPostLoginTermsOfUseFooterlink_Validation_PROD or @SensaPostLoginPrivacyPolicyFooterlink_Validation_PROD or @SensaPostLoginextMessagingFooterlink_Validation_PROD or @SensaHomePage_Validation_PROD or  @SensaLoginPage_Validation_PROD  or @SensaInValidData_Validation_PROD  or @SensaRememberMe_Validation_PROD or  @SensaDevice_Validation_PROD or @SensaOffersPage_Validation_PROD  or @SensaLSGW_Validation_PROD or @Sensa_Storelocator_UseMyLocationAndZipcode_Validation_PROD or @Sensa_Storelocator_UseMyLocation_Validation_PROD or @Sensa_Storelocator_FilterByProduct_Validation_PROD',
    timeout: 1600000,
    ignoreUndefinedDefinitions: false,
    retry: 0,
    retryTagFilter: '', // Only retry scenarios tagged with @flaky
    scenarioLevelReporter: true,
  },
};
