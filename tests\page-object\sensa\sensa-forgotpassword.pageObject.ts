class ForgotPasswordPageObject {

    get lnkforgotPassword_sensa() { return $('//*[@class="forgotPasswordLink"]'); }
    get hdrforgotPassword_sensa() { return $('//*[@class="cmp-register__forgotPasswordStepOne active"] //h3[@class="cmp-register__stepHeader-title"]'); }
    get lblwecanhelp_sensa() { return $('//p[contains(text(),"We can help! Please provide the following informat")]'); }
    get lblforgotUsername_sensa() { return $('(//*[@class="cmp-register__emailRegister"]//*[@class="cmp-register__sectionHeading"])[1]'); }
    get hdremail_sensa() { return $('(//*[@class="cmp-register__emailRegister"]//*[@class="cmp-register__sectionHeading"])[2]'); }
    get lblyourUsername_sensa() { return $('(//*[@class="cmp-register__emailRegister"]//*[@class="cmp-register__sectionDescription"])[1]'); }
    get lblyouremailaddress_sensa() { return $('(//*[@class="cmp-register__emailRegister"]//*[@class="cmp-register__sectionDescription"])[2]'); }
    get txtemail_sensa() { return $('//*[@class="cmp-register__input-email"]'); }
    get lblemail_sensa() { return $('//*[@id="labelEmail"]'); }
    get lblerroremail_sensa() { return $('//*[@class="validation wrongCheck"]'); }
    get lblstepLostPassword_sensa() { return $('//*[@class="cmp-register__stepLostPassword-warning"]'); }
    get lblcheckyouremail_sensa() { return $('(//h3[@class="cmp-register__stepHeader-title"])[2]'); }
    get lblwewillsendemail_sensa() { return $('//p[contains(text(),"We will send you an email with a link to reset you")]'); }
    get btncontinue_sensa() { return $('//form[@id="formLostPassword"]//button[@type="submit"]'); }
    get lnkgoto_sensa() { return $('//*[@class="cmp-register__login-link underlined-link"]'); }



    // ResetPassword
    get hdrResetPassword_sensa() { return $('//h3[normalize-space()="Reset your password"]'); }
    get lblResetPassworddesc_sensa() { return $('//p[contains(text(),"Your password must be a combination of 8-12 letter")]'); }
    get btncontinueToNextStep_sensa() { return $('button*=Continue to Next'); }

}
export default new ForgotPasswordPageObject();