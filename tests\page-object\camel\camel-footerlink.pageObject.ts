class CamelFooterlinkPageObject {


  get lnkPreFAQ_camel() { return $('//*[contains(text(),"FAQ")]'); }
  get hdrfaq_ContactUs_camel() { return $('//h2[text()="Contact Us"]'); }

  get hdrfaq_loginandpassword_camel() { return $('//h3[normalize-space()="LOGIN AND PASSWORD"]'); }
  get hdrfaq_offersandpromotions_camel() { return $('//h3[normalize-space()="OFFERS AND PROMOTIONS"]'); }
  get hdrfaq_ageverification_camel() { return $('//h3[normalize-space()="AGE VERIFICATION"]'); }
  get hdrfaq_privacy_camel() { return $('//h3[normalize-space()="PRIVACY"]'); }
  get hdrfaq_troubleshoot_camel() { return $('//h3[normalize-space()="TROUBLESHOOTING"]'); }
  get hdrfaq_general_camel() { return $('//h3[normalize-space()="General"]'); }
  get hdrfaq_environment_camel() { return $('//h3[normalize-space()="Environmental Stewardship"]'); }
  get lblpleasecall_camel() { return $('(//*[contains(text(),"Please call ************** and we\'ll gladly assist")])[1]'); }
  get hdrsitereq_camel() { return $('//h1[normalize-space()="Site Requirements"]'); }
  get hdrmobilereq_camel() { return $('(//*[@class="cmp-section__container"]//h2)[1]'); }
  get hdrdesktopreq_camel() { return $('(//*[@class="cmp-section__container"]//h2)[2]'); }
  get hdrimportant_camel() { return $('(//*[@class="cmp-section__container"]//h2)[3]'); }
  get hdreqdesktopandmobile_camel() { return $('(//*[@class="cmp-section__container"]//h2)[4]'); }

  get lblmobilebrowser_camel() { return $('(//*[@class="cmp-section__container"]//h3)[1]'); }
  get lblmobileos_camel() { return $('(//*[@class="cmp-section__container"]//h3)[2]'); }
  get lbldesktopbrowser_camel() { return $('(//*[@class="cmp-section__container"]//h3)[3]'); }
  get lbldesktopOS_camel() { return $('(//*[@class="cmp-section__container"]//h3)[4]'); }
  get lblpostlogindesktopbrowser_camel() { return $('(//*[@class="cmp-section__container"]//h3)[5]'); }
  get hdrjavascript_camel() { return $('(//*[@class="cmp-section__container"]//h3)[6]'); }
  get hdrjavascriptpostlogin_camel() { return $('(//*[@class="cmp-section__container"]//h3)[9]'); }
  get hdrcookies_camel() { return $('(//*[@class="cmp-section__container"]//h3)[7]'); }
  get hdrcookiespostlogin_camel() { return $('(//*[@class="cmp-section__container"]//h3)[10]'); }
  get lblimportantdesc_camel() { return $('(//*[@class="cmp-section__container"]//p)[5]'); }
  get lbljavscriptdesc_camel() { return $('(//*[@class="cmp-section__container"]//p)[6]'); }
  get lbljavscriptdescpostlogin_camel() { return $('(//*[@class="cmp-section__container"]//p)[3]'); }
  get lblcookiesdespostlogin_camel() { return $('(//*[@class="cmp-section__container"]//p)[4]'); }
  get lblcookiesdesc_camel() { return $('(//*[@class="cmp-section__container"]//p)[7]'); }


  get hdrtextMessaging_camel() { return $('//*[contains(text(),"TEXT MESSAGING TERMS AND CONDITIONS")]'); }


  get hdrtobaccoRights_camel() { return $('//*[contains(text(), "Your Voice Matters.")]'); }
  get lblownit_camel() { return $('//span[contains(text(), "Own It Voice It")]'); }
  get lblspeakup_camel() { return $('//*[contains(text(),"Speak up for your communit")]'); }
  get lblproposals_camel() { return $('//*[contains(text(),"Proposals in your state and in Washington, D.C")]'); }
  get btntakeactionNow_camel() { return $('//*[contains(text(),"Take Action Now")]'); }
  get icnYoutube_camel() { return $('a[title="Youtube"]'); }
  get icnInsta_camel() { return $('a[title="Instagram"]'); }
  get icnFacebook_camel() { return $('a[title="Facebook"]'); }
  get icnTwitter_camel() { return $('a[title="TwitterX"]'); }
  get btnacceptCookies_camel() { return $('#onetrust-accept-btn-handler'); }

  get lnkcontactUsPostLogin_camel() { return $('(//*[text()="Contact Us"])[2]'); }
  get hdrpostloginContactus_camel() { return $('//h1[text()="Contact Us"]'); }
  get lblhours_camel() { return $('(//*[@class="cmp-grid-column"]//h4)'); }


  get lnkSiteRequirementsPostLogin_camel() { return $('(//*[text()="Site Requirements"])[2]'); }

  get hdrcamelPoints_camel() { return $('.cmp-section__container h1'); }
  get lblcamelPointsterms_camel() { return $('.cmp-section__container h2'); }
  get lblcamelPointsparara1_camel() { return $$('.cmp-section__container p')[0]; }
  get lblcamelPointsparara2_camel() { return $$('.cmp-section__container p')[2]; }
  get lblcamelPointsparara3_camel() { return $$('.cmp-section__container p')[3]; }
  get lbleligibility_camel() { return $$('.cmp-section__container p')[4]; }
  get lbljoiningpgr_camel() { return $$('.cmp-section__container p')[5]; }
  get lblhowewecommunicate_camel() { return $$('.cmp-section__container p')[6]; }
  get lblhowtoearn_camel() { return $$('.cmp-section__container p')[7]; }
  get lblhowtoredeem_camel() { return $$('.cmp-section__container p')[8]; }
  get lblhowtoavoid_camel() { return $$('.cmp-section__container p')[9]; }
  get lblavailablerwards_camel() { return $$('.cmp-section__container p')[10]; }
  get lblreserved_camel() { return $$('.cmp-section__container p')[11]; }
  get lblprogram_camel() { return $$('.cmp-section__container p')[12]; }
  get lbladditional_camel() { return $$('.cmp-section__container p')[13]; }
  get lblclosing_camel() { return $$('.cmp-section__container p')[14]; }
  get lbldisclaimer_camel() { return $$('.cmp-section__container p')[15]; }
  get lblchanges_camel() { return $$('.cmp-section__container p')[16]; }
  get lblprivacy_camel() { return $$('.cmp-section__container p')[17]; }
  get lblindemnity_camel() { return $$('.cmp-section__container p')[18]; }
  get lblchoiceoflaw_camel() { return $$('.cmp-section__container p')[19]; }


  get lbleligibilitydesc1_camel() { return $$('.cmp-section__container p')[21]; }
  get lbleligibilitydesc2_camel() { return $$('.cmp-section__container p')[22]; }
  get lbleligibilitydesc3_camel() { return $$('.cmp-section__container p')[23]; }
  get lbleligibilitydesc4_camel() { return $$('.cmp-section__container p')[24]; }
  get lbleligibilitydesc5_camel() { return $$('.cmp-section__container p')[25]; }
  get lbljoiningpgrdesc1_camel() { return $$('.cmp-section__container p')[26]; }
  get lbljoiningpgrdesc2_camel() { return $$('.cmp-section__container p')[27]; }
  get lbljoiningpgrdesc3_camel() { return $$('.cmp-section__container p')[28]; }
  get lblhowewecommunicatedesc1_camel() { return $$('.cmp-section__container p')[29]; }
  get lblhowewecommunicatedesc2_camel() { return $$('.cmp-section__container p')[30]; }
  get lblhowewecommunicatedesc3_camel() { return $$('.cmp-section__container p')[31]; }
  get lblhowtoearndesc1_camel() { return $$('.cmp-section__container p')[32]; }
  get lblhowtoearndesc2_camel() { return $$('.cmp-section__container p')[33]; }
  get lblhowtoearndesc3_camel() { return $$('.cmp-section__container p')[34]; }
  get lblhowtoearndesc4_camel() { return $$('.cmp-section__container p')[35]; }
  get lblhowtoearndesc5_camel() { return $$('.cmp-section__container p')[36]; }
  get lblhowtoearndesc6_camel() { return $$('.cmp-section__container p')[37]; }
  get lblhowtoearndesc7_camel() { return $$('.cmp-section__container p')[38]; }
  get lblhowtoearndesc8_camel() { return $$('.cmp-section__container p')[39]; }
  get lblhowtoearndesc9_camel() { return $$('.cmp-section__container p')[40]; }
  get lblhowtoearndesc10_camel() { return $$('.cmp-section__container p')[41]; }
  get lblhowtoearndesc11_camel() { return $$('.cmp-section__container p')[42]; }
  get lblhowtoearndesc12_camel() { return $$('.cmp-section__container p')[43]; }
  get lblhowtoearndesc13_camel() { return $$('.cmp-section__container p')[44]; }

  get lblhowtoredeemdesc1_camel() { return $$('.cmp-section__container p')[45]; }
  get lblhowtoredeemdesc2_camel() { return $$('.cmp-section__container p')[46]; }

  get lblhowtoavoiddesc1_camel() { return $$('.cmp-section__container p')[47]; }
  get lblhowtoavoiddesc2_camel() { return $$('.cmp-section__container p')[48]; }
  get lblhowtoavoiddesc3_camel() { return $$('.cmp-section__container p')[49]; }

  get lblavailablerwardsdesc1_camel() { return $$('.cmp-section__container p')[50]; }
  get lblavailablerwardsdesc2_camel() { return $$('.cmp-section__container p')[51]; }
  get lblavailablerwardsdesc3_camel() { return $$('.cmp-section__container p')[52]; }
  get lblavailablerwardsdesc4_camel() { return $$('.cmp-section__container p')[53]; }
  get lblavailablerwardsdesc5_camel() { return $$('.cmp-section__container p')[54]; }
  get lblavailablerwardsdesc6_camel() { return $$('.cmp-section__container p')[55]; }
  get lblavailablerwardsdesc7_camel() { return $$('.cmp-section__container p')[56]; }
  get lblreserveddesc1_camel() { return $$('.cmp-section__container p')[57]; }
  get lblreserveddesc2_camel() { return $$('.cmp-section__container p')[58]; }

  get lblprogramdesc1_camel() { return $$('.cmp-section__container p')[59]; }
  get lblprogramdesc2_camel() { return $$('.cmp-section__container p')[60]; }
  get lblprogramdesc3_camel() { return $$('.cmp-section__container p')[61]; }
  get lblprogramdesc4_camel() { return $$('.cmp-section__container p')[62]; }
  get lblprogramdesc5_camel() { return $$('.cmp-section__container p')[63]; }
  get lblprogramdesc6_camel() { return $$('.cmp-section__container p')[64]; }
  get lblprogramdesc7_camel() { return $$('.cmp-section__container p')[65]; }
  get lblprogramdesc8_camel() { return $$('.cmp-section__container p')[66]; }
  get lblprogramdesc9_camel() { return $$('.cmp-section__container p')[67]; }
  get lblprogramdesc10_camel() { return $$('.cmp-section__container p')[68]; }
  get lbladditionaldesc1_camel() { return $$('.cmp-section__container p')[69]; }
  get lbladditionaldesc2_camel() { return $$('.cmp-section__container p')[70]; }
  get lbladditionaldesc3_camel() { return $$('.cmp-section__container p')[71]; }
  get lbladditionaldesc4_camel() { return $$('.cmp-section__container p')[72]; }
  get lbladditionaldesc5_camel() { return $$('.cmp-section__container p')[73]; }
  get lbladditionaldesc6_camel() { return $$('.cmp-section__container p')[74]; }
  get lblclosingdesc1_camel() { return $$('.cmp-section__container p')[75]; }
  get lblclosingdesc2_camel() { return $$('.cmp-section__container p')[76]; }
  get lblclosingdesc3_camel() { return $$('.cmp-section__container p')[77]; }
  get lblclosingdesc4_camel() { return $$('.cmp-section__container p')[78]; }
  get lbldisclaimerdesc1_camel() { return $$('.cmp-section__container p')[79]; }
  get lbldisclaimerdesc2_camel() { return $$('.cmp-section__container p')[80]; }
  get lbldisclaimerdesc3_camel() { return $$('.cmp-section__container p')[81]; }
  get lbldisclaimerdesc4_camel() { return $$('.cmp-section__container p')[82]; }
  get lblchangesdesc1_camel() { return $$('.cmp-section__container p')[83]; }
  get lblchangesdesc2_camel() { return $$('.cmp-section__container p')[84]; }
  get lblprivacydesc1_camel() { return $$('.cmp-section__container p')[85]; }
  get lblprivacydesc2_camel() { return $$('.cmp-section__container p')[86]; }
  get lblprivacydesc3_camel() { return $$('.cmp-section__container p')[87]; }
  get lblprivacydesc4_camel() { return $$('.cmp-section__container p')[88]; }
  get lblprivacydesc5_camel() { return $$('.cmp-section__container p')[89]; }
  get lblprivacydesc6_camel() { return $$('.cmp-section__container p')[90]; }
  get lblprivacydesc7_camel() { return $$('.cmp-section__container p')[91]; }
  get lblprivacydesc8_camel() { return $$('.cmp-section__container p')[92]; }
  get lblindemnitydesc1_camel() { return $$('.cmp-section__container p')[93]; }
  get lblindemnitydesc2_camel() { return $$('.cmp-section__container p')[94]; }
  get lblchoiceoflawdesc1_camel() { return $$('.cmp-section__container p')[95]; }
  get lblchoiceoflawdesc2_camel() { return $$('.cmp-section__container p')[96]; }

  get lnktermsofUsepostlogin_sensa() { return $('(//*[text()="Terms of Use"])[2]'); }
   get lnkprivacypolicy_camel() { return $('(//*[contains(text(),"Privacy Policy")])[2]'); }
   get lnktextmessaging_camel() { return $('(//*[contains(text(),"Text Messaging Terms & Conditions")])[2]'); }
    get lnksustainabilitypostlogin_camel() { return $('(//*[text()="Sustainability"])[2]'); }

  get hdrsustainability_camel() { return $('.brand'); }

}
export default new CamelFooterlinkPageObject();