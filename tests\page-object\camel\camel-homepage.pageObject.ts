class CamelHomePageObject {

    get lnkproducts_camel() { return $('a[title="Products"]'); }
    get lnkstoreLocator_camel() { return $('a[title="Store Locator"]'); }

    get lnkcamelrewards_camel() { return $('.rewardstatus'); }
    get lblwarningHeadline_camel() { return $('.cmp-doj-warning__headline'); }
    get lblwelcometocamel_camel() { return $('//div[@id="welcome"]//div[@class="cmp-section__container"]'); }
    get lblwelcometocameldesc_camel() { return $$('.cmp-lplus-enrollment__content')[1]; }
    get lblwelcometocameldesc_prod_camel() { return $('(//div[@class="cmp-lplus-enrollment__content"])[2]'); }
    get lnkhowitworks_camel() { return $('(//a[@title="How it works"])'); }
    get hdrhowitworks_camel() { return $('//h3[normalize-space()="HOW CAMEL POINTS WORK"]'); }
    get lblhumptitle_camel() { return $('#shadow'); }
    get btncheckitout_camel() { return $('a[aria-label="Check It Out"]'); }
    get imghump_camel() { return $('//*[@alt="the hump"]'); }
    get lblfindyourfavourites_camel() { return $('//*[text()="Find Your Favorites"]'); }
    get hdrfindyourfavourites_camel() { return $('//h2[contains(text(),"FIND YOUR")]'); }
    get lblyourtast_camel() { return $('//*[text()="Your Taste, Your Style"]'); }
    get btnexplore_camel() { return $('//*[@aria-label="Explore Now"]'); }
    get imgproduct_camel() { return $$('.cmp-image__image')[11]; }
    get imgdaretodiscover_camel() { return $$('.cmp-sms-signup img')[1]; }
    get lblhambergercloseicon_camel() { return $('//div[contains(@class,"mobile-nav-icon open")]'); }

    get lnkthesestatements_camel() { return $('.cmp-doj-warning__headline a'); }
    get lblthesestatementsdesc1_camel() { return $$('.cmp-doj-warning__modal-content p')[0]; }
    get lblthesestatementsdesc2_camel() { return $$('.cmp-doj-warning__modal-content p')[1]; }
    get lblthesestatementsdesc3_camel() { return $$('.cmp-doj-warning__modal-content ul')[0]; }
    get lblthesestatementsdesc4_camel() { return $$('.cmp-doj-warning__modal-content p')[2]; }
    get lblthesestatementsdesc5_camel() { return $$('.cmp-doj-warning__modal-content p')[3]; }
    get lblthesestatementsdesc6_camel() { return $$('.cmp-doj-warning__modal-content ul')[1]; }
    get lblthesestatementsdesc7_camel() { return $$('.cmp-doj-warning__modal-content p')[4]; }
    get lblthesestatementsdesc8_camel() { return $$('.cmp-doj-warning__modal-content p')[5]; }
    get lblthesestatementsdesc9_camel() { return $$('.cmp-doj-warning__modal-content ul')[2]; }
    get lblthesestatementsdesc10_camel() { return $$('.cmp-doj-warning__modal-content p')[6]; }
    get lblthesestatementsdesc11_camel() { return $$('.cmp-doj-warning__modal-content p')[7]; }
    get lblthesestatementsdesc12_camel() { return $$('.cmp-doj-warning__modal-content ul')[3]; }
    get lblthesestatementsdesc13_camel() { return $$('.cmp-doj-warning__modal-content p')[8]; }
    get lblthesestatementsdesc14_camel() { return $$('.cmp-doj-warning__modal-content p')[9]; }
    get lblthesestatementsdesc15_camel() { return $$('.cmp-doj-warning__modal-content ul')[4]; }
    get lblthesestatementsdesc16_camel() { return $$('.cmp-doj-warning__modal-content p')[10]; }
    get lblthesestatementsdesc17_camel() { return $$('.cmp-doj-warning__modal-content p')[11]; }
    get lblthesestatementsdesc18_camel() { return $$('.cmp-doj-warning__modal-content ul')[5]; }
    get lblthesestatementsdesc19_camel() { return $$('.cmp-doj-warning__modal-content p')[12]; }
    get lblthesestatementsdesc20_camel() { return $$('.cmp-doj-warning__modal-content p')[13]; }
    get lblthesestatementsdesc21_camel() { return $$('.cmp-doj-warning__modal-content ul')[6]; }
    get lblthesestatementsdesc22_camel() { return $$('.cmp-doj-warning__modal-content p')[14]; }
    get lblthesestatementsdesc23_camel() { return $$('.cmp-doj-warning__modal-content p')[15]; }
    get lblthesestatementsdesc24_camel() { return $$('.cmp-doj-warning__modal-content ul')[7]; }
    get lblthesestatementsdesc25_camel() { return $$('.cmp-doj-warning__modal-content p')[16]; }
    get lblthesestatementsdesc26_camel() { return $$('.cmp-doj-warning__modal-content p')[17]; }
    get lblthesestatementsdesc27_camel() { return $$('.cmp-doj-warning__modal-content ul')[8]; }
    get lblthesestatementsdesc28_camel() { return $$('.cmp-doj-warning__modal-content p')[18]; }
    get lblthesestatementsdesc29_camel() { return $$('.cmp-doj-warning__modal-content p')[19]; }
    get lblthesestatementsdesc30_camel() { return $$('.cmp-doj-warning__modal-content ul')[9]; }
    get lblclosefebanner_camel() { return $('//div[@class="cmp-doj-warning__close icon-close"]'); }
     get lblproductpageheader_camel() { return $('(//span[@class="cmp-hero-banner__headline1"])[3]'); }
    

}
export default new CamelHomePageObject();