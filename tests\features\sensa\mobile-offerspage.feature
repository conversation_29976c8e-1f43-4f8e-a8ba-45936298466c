Feature: Offers Page Validation.

    Scenario Outline: Updating Offers Page Details for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on Offers Link
        Then The user validates offers page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user click on Claim Mobile Offers button
        Then The user validates that successfully logged out

        @SensaOffersPage_Validation_QA  
        Examples:
            | Brand | URL                             | Username                            | Password  | filename              | sheetname | scenarioname         |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | aem-mobile-sensa.json | Offers    | Validate Offers Page |

        @SensaOffersPage_Validation_PROD  
        Examples:
            | Brand | URL                       | Username                             | Password  | filename              | sheetname | scenarioname         |
            | Sensa | https://www.sensavape.com |<EMAIL> | Password1 | aem-mobile-sensa.json | Offers    | Validate Offers Page |
