# WebDriverIO Test Execution Issues - Fixes Applied

## Issues Identified and Fixed

### 1. **Excel to JSON Conversion Running Multiple Times**

**Problem:**
- The `beforeSession` hook was being called for every worker process
- Each parallel worker was independently converting the Excel file to JSON
- This caused unnecessary overhead and multiple conversion messages

**Solution Applied:**
- Moved Excel to JSON conversion to the `onPrepare` hook which runs only once before all workers start
- Added a global flag `_excelConversionCompleted` to track conversion status
- Simplified `beforeSession` hook to only verify JSON file existence
- Added timestamp comparison to only convert when Excel file is newer than JSON

**Benefits:**
- Excel conversion now happens only once per test run
- Reduced startup time for parallel workers
- Eliminated redundant conversion messages

### 2. **25 Workers Created for Single Tag**

**Problem:**
- Configuration had `specs: ['./tests/features/**/*.feature']` which includes ALL feature files
- WebDriverIO creates a worker for each spec file regardless of tag filtering
- Tag filtering happens at scenario level, not file level
- This created 25 workers even when running a single tagged scenario

**Solution Applied:**
- Changed specs configuration to target only the specific feature file containing the tagged scenario
- Updated to: `specs: ['./tests/features/sensa/mobile-forgotPasswordpage.feature']`
- This ensures only one worker is created for the specific feature file

**Benefits:**
- Only 1 worker created instead of 25
- Faster test execution
- Reduced resource consumption
- More targeted test runs

### 3. **Health Check URL Issue**

**Problem:**
- Tests were accessing `http://127.0.0.1:8100/health` as part of SauceLabs service initialization
- Previous configuration was interfering with this natural SauceLabs health check process
- Health checks were being treated as test scenarios instead of service initialization
- This caused confusion in test reporting and execution

**Solution Applied:**
- Removed interference with SauceLabs health check process
- Simplified session validation to not block health check URLs
- Let SauceLabs handle health checks naturally as part of service initialization
- Removed health check filtering from scenario processing
- Improved test naming to show clean scenario names in SauceLabs reports

**Benefits:**
- SauceLabs service can perform health checks without interference
- Cleaner test names in SauceLabs reports (scenario names only)
- More stable session initialization
- Proper separation between service health checks and actual test scenarios

### 3. **SIGINT Interruption Issues**

**Problem:**
- Tests were being interrupted with SIGINT signals causing premature termination
- Workers were shutting down gracefully but not completing test execution

**Solution Applied:**
- Improved error handling in session validation
- Added try-catch blocks around session operations
- Enhanced logging to identify when and why interruptions occur

### 4. **SauceLabs Test Reporting Improvements**

**Problem:**
- Test names in SauceLabs were not showing individual scenario names clearly
- Retry attempts were not properly tracked and reported

**Solution Applied:**
- Enhanced test naming in `beforeScenario` hook to include feature and scenario names
- Improved retry tracking with attempt numbers
- Added better context information for SauceLabs reporting
- Filtered out health check scenarios from SauceLabs reporting

## Configuration Changes Made

### `tests/configs/wdio.shared.conf.ts`

1. **Added `onPrepare` hook:**
   - Performs one-time Excel to JSON conversion
   - Runs before all workers start
   - Includes file timestamp checking

2. **Enhanced `isSessionValid()` function:**
   - Detects health check URLs
   - Better error handling
   - More detailed logging

3. **Improved `beforeScenario` hook:**
   - Enhanced URL validation
   - Better session management
   - Improved SauceLabs test naming

4. **Simplified `beforeSession` hook:**
   - Now only verifies JSON file existence
   - Reduced redundant operations

## Expected Results

After applying these fixes, you should see:

1. **Reduced startup time:** Excel conversion happens only once
2. **Proper test execution:** Tests run actual scenarios instead of health checks
3. **Better SauceLabs reporting:** Clear scenario names and retry information
4. **Fewer interruptions:** Improved error handling and session management
5. **Cleaner logs:** Reduced redundant messages and better debugging information

## Monitoring and Verification

To verify the fixes are working:

1. **Check logs for:** 
   - Single "Converting Excel to JSON" message at the start
   - Proper scenario names in test execution
   - No health check URLs in test runs

2. **SauceLabs dashboard should show:**
   - Individual scenario names as test names
   - Proper pass/fail status for retries
   - Clear feature and scenario context

3. **Performance improvements:**
   - Faster worker startup times
   - Reduced overall test execution time
   - More stable test runs

### **4. Step Definitions Not Found (Critical Fix)**

**Problem:**
- Tests were failing with "Step 'undefined' is not defined" errors
- Step definitions existed but were not being loaded by Cucumber
- Configuration was using `./tests/step-definitions/*.ts` which only loads files directly in the folder
- Step definitions are organized in subdirectories like `./tests/step-definitions/sensa/`

**Solution Applied:**
- Fixed Cucumber configuration to use recursive glob pattern: `./tests/step-definitions/**/*.ts`
- This loads step definitions from all subdirectories
- Removed unnecessary `beforeFeature` hook that was causing naming conflicts
- Simplified approach to let SauceLabs handle feature naming naturally

**Benefits:**
- Step definitions are now properly loaded and recognized
- Tests can execute actual scenarios instead of failing on undefined steps
- Cleaner configuration without unnecessary overrides
- Proper test execution flow

### **5. Health Check URL and Test Naming Issues**

**Problem:**
- Health check URL (`http://127.0.0.1:8100/health`) appears during SauceLabs initialization
- SauceLabs service automatically sets feature names as job names initially
- This is normal SauceLabs behavior for service initialization

**Solution Applied:**
- Allow SauceLabs to handle health checks naturally as part of service initialization
- Let the `beforeScenario` hook override the job name with the actual scenario name
- Removed interference with the natural SauceLabs initialization process
- Added proper timing to ensure scenario names are set correctly

**Benefits:**
- Health checks complete without interference
- Scenario names properly appear in SauceLabs reports
- Natural SauceLabs service initialization flow
- Better test execution stability

## Expected Results After All Fixes

1. **Only 1 worker** created instead of 25
2. **Health check URLs** handled naturally by SauceLabs without interference
3. **Scenario names** (not feature names) appear in SauceLabs reports
4. **Actual test scenarios** run instead of health check URLs
5. **Faster test execution** due to targeted feature file selection
6. **Proper retry handling** with clear attempt numbering
7. **Single Excel conversion** at startup instead of multiple conversions

## Additional Recommendations

1. **Monitor test stability:** Watch for any remaining SIGINT issues
2. **Review SauceLabs reports:** Verify test names and results are properly displayed
3. **Consider timeout adjustments:** If tests still timeout, review wait times in configuration
4. **Log analysis:** Use the enhanced logging to identify any remaining issues
