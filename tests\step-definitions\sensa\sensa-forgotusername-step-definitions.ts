import { Then, When } from '@wdio/cucumber-framework';
import sensaForgotusernamePage from '../../pages/sensa/sensa-forgotusername.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaAccountPageObject from '../../page-object/sensa/sensa-account.pageObject.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import sensaForgotusernamePageObject from '../../page-object/sensa/sensa-forgotusername.pageObject.ts';
const url = await browser.getUrl();

Then(/^The user clicks on Forgot Username Link$/, async function () {
    await sensaForgotusernamePage.clickonforgotUsername();
    await expect(sensaAccountPageObject.lbllegalName_sensa).toBeDisplayed();
    logger.info('Navigated to Forgot Username Page Successfully');
});

When(/^The user enters First Name as (.*) Last Name as (.*) street address as (.*) Zipcode as (.*) City as (.*) State as (.*) and DOB as "([^"]*)"$/, async function (firstName: string, lastName: string, currentaddr: string, zipcode: string, city: string, state: string, birthdate: string) {
    await sensaForgotusernamePage.entertellUsaboutpageDetailsinForgotusernamepage(firstName, lastName, currentaddr, zipcode, city, state, birthdate);
    logger.info('Entered Details Successfully');

});

When(/^The user enters answer as (.*) for the security question$/, async function (answer: string) {
    await sensaForgotusernamePage.enterdetailsinidentityPage(answer);
    logger.info('Entered Answer Successfully');
});

When(/^The username (.*) is verified and Password (.*) is entered$/, async function (username: string, password: string) {
    await sensaForgotusernamePage.verifyusernameandenterpasswordinaccessAccountPage(username, password);
    if (url.includes('sensa')) {
        await expect(sensaRegistrationPageObject.lbllogo_sensa).toBeDisplayed();
    } else {
        await expect(camelLoginPageObject.lbllogo_camel).toBeDisplayed();
    }
    logger.info('NAvigated to Home Page Successfully');
});

Then(/^The user validates Tell us about Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotusernamePage.tellUsaboutpageDetailsinForgotusernamepagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated the Content in Tellus about page Successfully');
});
Then(/^The user validates Verify Identity Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotusernamePage.verifyIdentitypagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated Verify Identity Page');
});

Then(/^The user validates Username Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotusernamePage.UsernamePagevalidation(filepath, sheetname, scenarioname);
    logger.info('Validated Verify Username Page');
});

When(/^The user enters wrong answer as (.*) for the security question and validates error Message (.*)$/, async function (answer: string, errormessage: string) {
    await sensaForgotusernamePage.wrongSecurityAnswer(answer, errormessage);
    await expect(sensaForgotusernamePageObject.lblerrormssg_sensa).toHaveText(errormessage);
    logger.info('Validated Error Message Successfully');
});

Then(/^The user reenters wrong answer as (.*) and navigates to contact us page$/, async function (answer: string) {
    await sensaForgotusernamePage.navigateToContactUsPage(answer);
    if ((await url).includes('camel')) {
        await expect(camelLoginPageObject.lblFAQcontactUs_camel).toBeDisplayed();
    } else {
        await expect(sensaForgotusernamePageObject.lblcontactUs_sensa).toBeDisplayed();
    }
    logger.info('Navigated to Contact us page Successfully');
});

Then(/^The user validates Errors in Tell us about Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotusernamePage.tellUsaboutpageAllErrorsvalidation(filepath, sheetname, scenarioname);
    logger.info('Validated the Errors in Tellus about page Successfully');
});

Then(/^The user validates Generic Error in Tell us about Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaForgotusernamePage.tellusPageErrorValidation(filepath, sheetname, scenarioname);
    logger.info('Validated Error Message Successfully');
});

Then(/^The user validates Errors in Verify Identity Page with filepath (.*) sheet name (.*) and scenario name (.*) and enters correct answer as (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string, answer: string) {
    await sensaForgotusernamePage.answerFieldErrorValidation(filepath, sheetname, scenarioname, answer);
     logger.info('Validated Error Message Successfully');
});

Then(/^The user clicks on Reset Your Password link$/, async function () {
    await sensaForgotusernamePage.clickonresetYourPassword();
     await expect(sensaForgotusernamePageObject.lblPasswordHeader_sensa).toBeDisplayed();
    logger.info('Navigated to Forgot Password Page Successfully');

});