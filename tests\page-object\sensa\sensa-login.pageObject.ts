class LoginPageObject {

    get lblalreadyhaveaccount_sensa() { return $('//p[@class="cmp-login__login-description"]'); }
    get lblforgot_sensa() { return $('//div[@class="cmp-login__login-forgetCred"]'); }
    get lblloginemail_sensa() { return $('//input[@id="loginUsername"]//preceding-sibling::label'); }
    get lblloginpassword_sensa() { return $('//input[@id="loginPassword"]//preceding-sibling::label'); }
    get lblloginrememberme_sensa() { return $('//input[@id="rememberMe"]//following-sibling::label'); }
    get lbljoin_sensa() { return $('//*[text()=" Join"]'); }
    get lbldonthaveaccount_sensa() { return $('//div[@class="cmp-login__login-description-title"]'); }
    get lbljoinemail_sensa() { return $('//*[@id="regEmail"]//preceding-sibling::label'); }
    get lbllegalnotice_sensa() { return $('//div[@class="cmp-login__register-legalNotice"]'); }
    get lnkfaq_sensa() { return $('(//*[text()="FAQ"])'); }
    get lnksiteRequirement_sensa() { return $('(//*[text()="Site Requirements"])'); }
    get lnkprivacyPolicy_sensa() { return $('(//*[text()="Privacy Policy"])'); }
    get lnkcontactUs_sensa() { return $('(//*[text()="Contact Us"])'); }
    get lnktermsofUse_sensa() { return $('(//*[text()="Terms of Use"])'); }
    get lnktextMessaging_sensa() { return $('(//*[text()="Text Messaging Terms & Conditions"])'); }
    get lblcopyright_sensa() { return $('//div[@class="cmp-footer__copyright col"]'); }
    get txtremembermeCheckbox_sensa() { return $('//input[@id="rememberMe"]'); }

}
export default new LoginPageObject();