/* eslint-disable @typescript-eslint/no-explicit-any */
import * as mssql from 'mssql';

let dbConnection: mssql.ConnectionPool;

export class DBUtilities {
    async connectDB(dbUsername: string, dbPassword: string, dbServerName: string) {
        const config: mssql.config = {
            user: dbUsername,
            password: dbPassword,
            server: dbServerName,
            port: 1433,
            options: {
                encrypt: true,
                trustServerCertificate: true,
            },
        };

        dbConnection = await new mssql.ConnectionPool(config).connect();
    }

    async query(queryString: string): Promise<mssql.IResult<any>> {
        return dbConnection.request().query(queryString);
    }

    async closeConnection(): Promise<void> {
        try {
            if (dbConnection.connected) {
              await dbConnection.close();
            }
          } catch (error) {
            console.error('Error closing database connection:', error);
          }
    }
}