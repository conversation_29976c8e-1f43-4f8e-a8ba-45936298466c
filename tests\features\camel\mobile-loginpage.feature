Feature: Login Page Validation in Camel Brand.

    Scenario Outline: Validate Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        Then The user Validates camel Signin Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand

        @CamelLogin_Validation_QA 
        Examples:
            | Brand | URL                         | Username                           | Password  | filename              | sheetname | scenarioname          |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Login     | Login Page Validation |

        @CamelLogin_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                             | Password  | filename              | sheetname | scenarioname          |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | aem-mobile-camel.json | Login     | Login Page Validation |

    Scenario Outline: Validate InValid Username and Password error in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with invalid user id <Username> or invalid password <invalidPassword>
        Then The user validates login error message <loginerror>
        When The user login with invalid user id <invalidUsername> or invalid password <Password>
        Then The user validates login error message <loginerror>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand

        @CamelInValidData_Validation_QA 
        Examples:
            | Brand | URL                         | Username                           | Password  | invalidPassword | invalidUsername                      | loginerror                                                         |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

        @CamelInValidData_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                             | Password  | invalidPassword | invalidUsername                       | loginerror                                                         |
            | Camel | https://www.camel.com | <EMAIL> | Password1 | Passcod#@e12    | <EMAIL> | Username or password does not match our records. Please try again. |

    Scenario Outline: Validate Rememeber Me Functionality in Login Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user enters valid user id <Username> and password <Password>
        Then The user clicks on Remember me checkbox and log in
        Then The user should be able to login to the camel application successfully
        Then The user validates that successfully logged out of camel brand
        Then The username <Username> should be displayed with remeber me checkbox selected

        @CamelRememberMe_Validation_QA  
        Examples:
            | Brand | URL                         | Username                           | Password  |
            | Camel | https://aem-stage.camel.com | <EMAIL> | Password1 |


        @CamelRememberMe_Validation_PROD 
        Examples:
            | Brand | URL                   | Username                             | Password  |
            | Camel | https://www.camel.com | <EMAIL> | Password1 |

