import { When, Then } from '@wdio/cucumber-framework';
import sensaMydevicePage from '../../pages/sensa/sensa-mydevice.page.ts';

When(/^The user clicks on My device link$/, async function () {
    await sensaMydevicePage.clickonmyDeviceLink();
});
Then(/^The user clicks on links$/, async function () {
    await sensaMydevicePage.clickoneachlinkandvalidatethevideo();
});

Then(/^The user Validates My Device Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaMydevicePage.myDevicepagevalidation(filepath,sheetname,scenarioname);
});