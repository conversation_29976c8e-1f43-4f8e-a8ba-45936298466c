import { When, Then } from '@wdio/cucumber-framework';
import sensaTextsignupPage from '../../pages/sensa/sensa-textsignup.page.ts';

When(/^The user clicks on Text Signup link$/, async function () {
    await sensaTextsignupPage.clickontextSignUpLink();
});

Then(/^The user Validates Text Sign-Up Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaTextsignupPage.signUpPageValidation(filepath, sheetname, scenarioname);
});

Then(/The user enters valid Mobile Number as (.*) and validates success message as (.*)$/, async function (mobileNumber: string, success: string) {
    await sensaTextsignupPage.entervalidMobileNumberandvalidatesuccessmssg(mobileNumber,success);
});

Then(/The user enters invalid Mobile Number as (.*) and validates error message as (.*)$/, async function (mobileNumber: string, success: string) {
    await sensaTextsignupPage.enterinvalidMobileNumberandvalidateerror(mobileNumber,success);
});