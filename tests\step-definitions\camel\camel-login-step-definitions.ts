import { Then} from '@wdio/cucumber-framework';
import camelLoginPage from '../../pages/camel/camel-login.page.ts';

Then(/^The user Validates camel Signin Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await camelLoginPage.camelloginPageValidation(filepath, sheetname, scenarioname);
});

Then(/^The user should be able to login to the camel application successfully$/, async function () {
    await camelLoginPage.camelloginPage();
});

Then(/^The user validates that successfully logged out of camel brand$/, async function () {
    await camelLoginPage.camellogoutsucessfully();
});





