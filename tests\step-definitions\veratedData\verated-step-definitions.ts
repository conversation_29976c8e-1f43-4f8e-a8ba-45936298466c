import { Given, When, Then } from '@wdio/cucumber-framework';
import VeartedData from '../../pages/veratedData/veratedData.page.ts';


Given(/^The user launches the site "([^"]*)"$/, async function (siteUrl: string): Promise<void> {
    await VeartedData.launchUrl(siteUrl);
});

When(/^The user clicks on AddTestCase button$/, async function () {
    await VeartedData.addTestCaseButton();
});
Then(/^The user enters details in verated app with address "([^"]*)" city "([^"]*)" state "([^"]*)" and Zipcode "([^"]*)"$/, async function (addr: string, city: string, state: string, zipcode: string) {
    await VeartedData.VeratedDataDetails(addr, city, state, zipcode);
});

