import sensaHomepagePage from '../../pages/sensa/sensa-homepage.page.ts';
import { Then } from '@wdio/cucumber-framework';
import sensaSgwPage from '../../pages/sensa/sensa-sgw.page.ts';

Then(/^The user click on Flavours link$/, async function () {
    await sensaHomepagePage.clickonflavorsfromheader();
});
Then(/^The user click on Store Locator link$/, async function () {
    await sensaHomepagePage.storeLocatorPage();
});
Then(/^The user Validates SGW Message with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await sensaSgwPage.compareSGWText(filepath, sheetname, scenarioname);
});