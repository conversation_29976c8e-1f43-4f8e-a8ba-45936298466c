import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import sensaForgotpasswordPageObject from '../../page-object/sensa/sensa-forgotpassword.pageObject.ts';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import sensaAccountPage from './sensa-account.page.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';

class ForgotPasswordPage {

    async clickonforgotPasswordlink() {
        try {
            const url = browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.clickusingJavascript(sensaForgotpasswordPageObject.lnkforgotPassword_sensa);
            } else {
                await elementActions.click(sensaForgotpasswordPageObject.lnkforgotPassword_sensa);
            }
            await elementActions.assertion(sensaForgotpasswordPageObject.hdrforgotPassword_sensa);
            console.log('Navigated to Forgot Password Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Forgot Password Page', { error });
            throw error;
        }
    }

    async entervalidemailpage(email: string) {
        try {
            await elementActions.waitForDisplayed(sensaForgotpasswordPageObject.txtemail_sensa);
            const textbox = await sensaForgotpasswordPageObject.txtemail_sensa;
            await textbox.setValue(email);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regEmail') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, sensaForgotpasswordPageObject.txtemail_sensa);
            await elementActions.clickusingJavascript(sensaForgotpasswordPageObject.btncontinue_sensa);
            await elementActions.assertion(sensaForgotpasswordPageObject.lblcheckyouremail_sensa);
            console.log('Entered Email Successfully');
        } catch (error) {
            logger.error('Failed to Enter Email', { error });
            throw error;
        }
    }

    async enterinvalidemailpage(email: string, errormessage: string) {
        try {
            const textbox = await sensaForgotpasswordPageObject.txtemail_sensa;
            await textbox.setValue(email);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regEmail') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, sensaForgotpasswordPageObject.txtemail_sensa);
            await $('body').click();
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblerroremail_sensa, errormessage);
            console.log('Navigated to Forgot Password Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Forgot Password Page', { error });
            throw error;
        }
    }

    async entervalidPassword(password: string, Cpassword: string) {
        try {
            await elementActions.setValue(sensaRegistrationPageObject.txtpassword_sensa, password);
            const textbox = await sensaRegistrationPageObject.txtconfirmPassword_sensa;
            await textbox.setValue(Cpassword);
            await browser.execute((_selector) => {
                const el = document.querySelector('#regConfirmPassword') as HTMLInputElement;
                if (el) {
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, await sensaRegistrationPageObject.txtconfirmPassword_sensa);
            await elementActions.clickusingJavascript(sensaForgotpasswordPageObject.btncontinueToNextStep_sensa);
            console.log('Entered Password Successfully');
        } catch (error) {
            logger.error('Failed to Enter Password', { error });
            throw error;
        }
    }


    async forgotPasswordpagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrforgotPassword = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrforgotPassword');
            const lblwecanhelp = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwecanhelp');
            const lblforgotUsername = testData.getCellValue(SHEET_NAME, scenarioname, 'lblforgotUsername');
            const lblyourUsername = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyourUsername');
            const hdremail = testData.getCellValue(SHEET_NAME, scenarioname, 'hdremail');
            const lblyouremailaddress = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyouremailaddress');
            const lblemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblemail');
            const lblstepLostPassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstepLostPassword');
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.hdrforgotPassword_sensa, hdrforgotPassword);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblwecanhelp_sensa, lblwecanhelp);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblforgotUsername_sensa, lblforgotUsername);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblyourUsername_sensa, lblyourUsername);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.hdremail_sensa, hdremail);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblyouremailaddress_sensa, lblyouremailaddress);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblemail_sensa, lblemail);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblstepLostPassword_sensa, lblstepLostPassword);
            await elementActions.assertion(sensaForgotpasswordPageObject.btncontinue_sensa);
            await elementActions.assertion(sensaForgotpasswordPageObject.lnkgoto_sensa);
            console.log('Validated the Forgot Password page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Forgot Password page', { error });
            throw error;
        }
    }

    async resetPasswordpagevalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrResetPassword = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrResetPassword');
            const lblResetPassworddesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblResetPassworddesc');
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.hdrResetPassword_sensa, hdrResetPassword);
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblResetPassworddesc_sensa, lblResetPassworddesc);
            const hdrpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'ttlPassword');
            const lblchooseapassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchooseapassword');
            const lblpasswordCondition1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition1');
            const lblpasswordCondition2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition2');
            const lblpasswordCondition3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition3');
            const lblpasswordCondition4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpasswordCondition4');
            const lblpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpassword');
            const lblcpassword = testData.getCellValue(SHEET_NAME, scenarioname, 'lblconfirmpassword');
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.hdrPassword_sensa, hdrpassword);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpleasechoose_sensa, lblchooseapassword);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition1_sensa, lblpasswordCondition1);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition2_sensa, lblpasswordCondition2);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition3_sensa, lblpasswordCondition3);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordCondition4_sensa, lblpasswordCondition4);
            await elementActions.click(sensaRegistrationPageObject.txtpassword_sensa);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblpasswordlabel_sensa, lblpassword);
            await elementActions.click(sensaRegistrationPageObject.txtconfirmPassword_sensa);
            await sensaAccountPage.mssgcomparision(sensaRegistrationPageObject.lblcpasswordlabel_sensa, lblcpassword);
            await elementActions.assertion(sensaRegistrationPageObject.txtpassword_sensa);
            await elementActions.assertion(sensaRegistrationPageObject.txtconfirmPassword_sensa);
            await elementActions.assertion(sensaForgotpasswordPageObject.btncontinueToNextStep_sensa);
            console.log('Validated the Reset Password page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Reset Password page', { error });
            throw error;
        }
    }

    async resetSuccessvalidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const lblcheckyouremail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcheckyouremail');
            const lblwewillsendemail = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwewillsendemail');
            await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblcheckyouremail_sensa, lblcheckyouremail);
            const url = browser.getUrl();
            if ((await url).includes('sensa')) {
                await sensaAccountPage.mssgcomparision(sensaForgotpasswordPageObject.lblwewillsendemail_sensa, lblwewillsendemail);
            }
            console.log('Validated the Reset Request Success page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Reset Request Success page page', { error });
            throw error;
        }
    }


}
export default new ForgotPasswordPage();