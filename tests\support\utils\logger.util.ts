// test/support/utils/logger.util.ts
import winston from 'winston';

class Logger {
    private logger: winston.Logger;
    private static instance: Logger;

    private constructor() {
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.printf(({ timestamp, level, message, sessionId, ...metadata }) => {
                    // Include Saucelabs session ID in logs for easy correlation
                    const sessionInfo = sessionId ? `[Session: ${sessionId}] ` : '';
                    const metadataStr = Object.keys(metadata).length ?
                        ` | ${JSON.stringify(metadata)}` : '';
                    return `${timestamp} ${sessionInfo}[${level}]: ${message}${metadataStr}`;
                }),
            ),
            transports: [
                new winston.transports.Console({
                    format: winston.format.colorize({ all: true }),
                }),
            ],
        });
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private async getSessionInfo() {
        const sessionId = browser.sessionId;
        const capabilities = browser.capabilities as Record<string, unknown>;
        // Safely access Sauce Labs job ID
        const sauceOptions = capabilities['sauce:options'] as Record<string, unknown> | undefined;
        const sauceJob = capabilities['sauce:job'] as Record<string, unknown> | undefined;
        const sauceJobId = sauceOptions?.['job-name'] || sauceJob?.id;
        return {
            sessionId,
            capabilities,
            sauceJobId,
        };
    }

    /**
     * Process message and metadata to handle Error objects consistently
     */
    private processMessageAndMetadata(message: string | Error, metadata: object = {}) {
        let processedMessage: string;
        let processedMetadata = { ...metadata };

        if (message instanceof Error) {
            processedMessage = message.message;
            processedMetadata = {
                ...processedMetadata,
                stackTrace: message.stack,
                errorName: message.name,
            };
        } else {
            processedMessage = message;
        }

        // Check if metadata contains an error object
        if (metadata && 'error' in metadata && metadata.error instanceof Error) {
            const error = metadata.error as Error;
            processedMetadata = {
                ...processedMetadata,
                errorMessage: error.message,
                stackTrace: error.stack,
                errorName: error.name,
            };
        }

        return { message: processedMessage, metadata: processedMetadata };
    }

    /**
     * Log debug level messages
     */
    async debug(message: string | Error, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        const processed = this.processMessageAndMetadata(message, metadata);
        this.logger.debug(processed.message, { ...sessionInfo, ...processed.metadata });
    }

    /**
     * Log info level messages
     */
    async info(message: string | Error, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        const processed = this.processMessageAndMetadata(message, metadata);
        this.logger.info(processed.message, { ...sessionInfo, ...processed.metadata });
    }

    /**
     * Log error level messages
     */
    async error(message: string | Error, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        const processed = this.processMessageAndMetadata(message, metadata);

        this.logger.error(processed.message, {
            ...sessionInfo,
            ...processed.metadata,
        });

        // If we have a Saucelabs session, mark the test as failed
        if (browser?.execute && sessionInfo.sauceJobId) {
            await browser.execute('sauce:job-result=failed');
        }
    }

    /**
     * Log warning level messages
     */
    async warn(message: string | Error, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        const processed = this.processMessageAndMetadata(message, metadata);
        this.logger.warn(processed.message, { ...sessionInfo, ...processed.metadata });
    }
}

export default Logger.getInstance();
