import axios from 'axios';
import { faker } from '@faker-js/faker';
import https from 'https';

export interface VeratadTestData {
    fn: string;     // First name
    ln: string;     // Last name  
    addr: string;   // Street address
    city: string;   // City
    state: string;  // State
    zip: string;    // ZIP code
    phone: string;  // Phone
    email: string;  // Email
    ssn: string;    // SSN
    dob: string;    // DOB (YYYYMMDD)
}

export interface VeratadResponse {
    result: string;
}

export interface SuccessfulSubmission {
    submittedData: VeratadTestData;
    fakerData: {
        firstName: string;
        lastName: string;
        phone: string;
        email: string;
        ssn: string;
        dob: string;
    };
    addressData: {
        source: 'radar_api' | 'fallback';
        address: string;
        city: string;
        state: string;
        zip: string;
    };
    apiResponse: VeratadResponse;
}

export class VeratadTestDataUtil {
    private static readonly API_URL = 'https://be.demo.veratad.app/testing/insert-vdp.php';
    private static readonly RADAR_API_KEY = process.env.RADAR_API_KEY;

    // Real addresses for fallback
    private static readonly ADDRESSES = [
        { addr: '1600 Pennsylvania Avenue NW', city: 'Washington', state: 'DC', zip: '20500' },
        { addr: '350 5th Ave', city: 'New York', state: 'NY', zip: '10118' },
        { addr: '1 Apple Park Way', city: 'Cupertino', state: 'CA', zip: '95014' },
        { addr: '410 Terry Ave N', city: 'Seattle', state: 'WA', zip: '98109' },
        { addr: '233 S Wacker Dr', city: 'Chicago', state: 'IL', zip: '60606' },
        { addr: '1 Hacker Way', city: 'Menlo Park', state: 'CA', zip: '94025' },
        { addr: '111 8th Ave', city: 'New York', state: 'NY', zip: '10011' },
        { addr: '1900 Embarcadero Rd', city: 'Palo Alto', state: 'CA', zip: '94303' },
    ];

    /**
     * Generate test data, submit to Veratad API, and return details if successful
     */
    static async generateAndSubmit(): Promise<SuccessfulSubmission> {
        // Generate fake personal data
        const firstName = faker.person.firstName();
        const lastName =this.generateLastName(15);
        const phone = faker.phone.number();
        const ssn = this.generateSSN();
         const randomNumber = String(Math.floor(Math.random() * (9999 - 1001 + 1)) + 1001); // Generate a random number between 1001 and 999
        const email = `rjrautomationtest+${firstName}${randomNumber}${lastName}@gmail.com`;
        const dob = this.generateDOB();

        // Get real address
        const addressInfo = await this.getAddress();

        // Create test data
        const testData: VeratadTestData = {
            fn: firstName,
            ln: lastName,
            addr: addressInfo.addr,
            city: addressInfo.city,
            state: addressInfo.state,
            zip: addressInfo.zip,
            phone: phone,
            email: email,
            ssn: ssn,
            dob: dob,
        };

        // Submit to Veratad API
        const response = await this.submitToVeratad(testData);

        // Return details only if successful
        if (response.result === 'success') {
            return {
                submittedData: testData,
                fakerData: {
                    firstName: firstName,
                    lastName: lastName,
                    phone: phone,
                    email: email,
                    ssn: ssn,
                    dob: dob,
                },
                addressData: {
                    source: addressInfo.source,
                    address: addressInfo.addr,
                    city: addressInfo.city,
                    state: addressInfo.state,
                    zip: addressInfo.zip,
                },
                apiResponse: response,
            };
        } else {
            throw new Error(`Veratad API failed: ${JSON.stringify(response)}`);
        }
    }


    // Private helper methods
    private static async getAddress(): Promise<{ addr: string; city: string; state: string; zip: string; source: 'radar_api' | 'fallback' }> {
        // Try Radar API first if available
        if (this.RADAR_API_KEY) {
            try {
                const baseAddr = this.ADDRESSES[Math.floor(Math.random() * this.ADDRESSES.length)];

                // Create HTTPS agent for Radar API as well
                const httpsAgent = new https.Agent({
                    rejectUnauthorized: false,
                });

                const response = await axios.get('https://api.radar.io/v1/addresses/validate', {
                    params: {
                        streetAddress: baseAddr.addr,
                        city: baseAddr.city,
                        stateCode: baseAddr.state,
                        postalCode: baseAddr.zip,
                        countryCode: 'US',
                    },
                    headers: { 'Authorization': this.RADAR_API_KEY },
                    httpsAgent: httpsAgent,
                    timeout: 5000,
                });

                const addr = response.data.address;
                return {
                    addr: `${addr.number} ${addr.street}${addr.unit ? ' ' + addr.unit : ''}`,
                    city: addr.city,
                    state: addr.stateCode,
                    zip: addr.postalCode,
                    source: 'radar_api',
                };
            } catch (error) {
                console.warn(`Radar API failed, using fallback: ${error}`);
            }
        }

        // Use fallback address
        const fallback = this.ADDRESSES[Math.floor(Math.random() * this.ADDRESSES.length)];
        return { ...fallback, source: 'fallback' };
    }

    private static async submitToVeratad(data: VeratadTestData): Promise<VeratadResponse> {
        // Create HTTPS agent to handle SSL certificate issues
        const httpsAgent = new https.Agent({
            rejectUnauthorized: false, // Bypass SSL certificate verification
        });

        const response = await axios.post(this.API_URL, JSON.stringify(data), {
            headers: {
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
                'Content-Type': 'text/plain;charset=UTF-8',
                'Origin': 'https://rai.veratad.app',
                'Referer': 'https://rai.veratad.app/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0',
                'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Microsoft Edge";v="126"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            },
            httpsAgent: httpsAgent, // Add HTTPS agent to bypass SSL issues
            timeout: 30000,
        });

        return response.data;
    }

    private static generateSSN(): string {
        const area = faker.number.int({ min: 1, max: 665 });
        const group = faker.number.int({ min: 1, max: 99 });
        const serial = faker.number.int({ min: 1, max: 9999 });
        return `${area.toString().padStart(3, '0')}-${group.toString().padStart(2, '0')}-${serial.toString().padStart(4, '0')}`;
    }

    private static generateDOB(): string {
        const birthDate = faker.date.birthdate({ min: 20, max: 80, mode: 'age' });
        const year = birthDate.getFullYear();
        const month = (birthDate.getMonth() + 1).toString().padStart(2, '0');
        const day = birthDate.getDate().toString().padStart(2, '0');
        return `${year}${month}${day}`;
    }

    private static generateLastName(length:number) {
        let letters ='';
        for(let i=0;i<length;i++){
            const isUpper = Math.random()<0.5;
            const ascii=isUpper
            ?Math.floor(Math.random()*26)+65 //A-Z(ASCII 65-90)
            :Math.floor(Math.random()*26)+97; //a-z(ASCII 97-122)
            letters +=String.fromCharCode(ascii);
        }
        return letters;
    }
}