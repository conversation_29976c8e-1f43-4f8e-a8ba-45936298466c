/* eslint-disable @typescript-eslint/no-explicit-any */
import ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Converts Excel test data to JSON format for efficient parallel test execution
 */
export class ExcelToJsonConverter {
  private excelFilePath: string;
  private jsonFilePath: string;

  /**
   * Creates a new converter instance
   * @param excelFilePath Path to the Excel file containing test data
   * @param jsonFilePath Path where the JSON file will be saved (optional)
   */
  constructor(excelFilePath: string, jsonFilePath?: string) {
    this.excelFilePath = path.resolve(excelFilePath);
    this.jsonFilePath = jsonFilePath || path.resolve(path.dirname(excelFilePath),
      `${path.basename(excelFilePath, path.extname(excelFilePath))}.json`);
  }

  /**
   * Converts the Excel file to JSON format
   * @returns The path to the generated JSON file
   */
  async convertToJson(): Promise<string> {
    // Load the Excel workbook
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(this.excelFilePath);

    // Initialize the data structure to hold all test data
    const testData: Record<string, Record<string, Record<string, any>>> = {};

    // Process each worksheet
    workbook.worksheets.forEach(worksheet => {
      const sheetName = worksheet.name;
      testData[sheetName] = {};

      // Find the TestCaseName column index
      const headerRow = worksheet.getRow(1);
      let testCaseColumnIndex = -1;

      headerRow.eachCell({ includeEmpty: false }, (cell, colNumber) => {
        if (cell.value === 'TestCaseName') {
          testCaseColumnIndex = colNumber;
        }
      });

      // Skip this sheet if it doesn't have a TestCaseName column
      if (testCaseColumnIndex === -1) {
        console.warn(`Worksheet '${sheetName}' doesn't have a TestCaseName column, skipping.`);
        return;
      }

      // Extract column headers
      const headers: string[] = [];
      headerRow.eachCell({ includeEmpty: false }, (cell, colNumber) => {
        headers[colNumber] = String(cell.value);
      });

      // Process each data row
      worksheet.eachRow((row, rowNumber) => {
        // Skip the header row
        if (rowNumber === 1) return;

        // Get the test case name
        const testCaseName = String(row.getCell(testCaseColumnIndex).value);
        if (!testCaseName) return; // Skip rows without a test case name

        // Create an object for this test case
        const testCaseData: Record<string, any> = {};

        // Extract all cell values for this row
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          if (headers[colNumber]) {
            // Convert cell value based on type
            let value = cell.value;

            // Handle dates, formulas, etc.
            if (cell.type === 4) { // Date type
              value = cell.value ? new Date(cell.value as Date).toISOString() : null;
            } else if (typeof value === 'object' && value !== null && 'result' in value) {
              // Handle formula results
              value = (value as any).result;
            }

            testCaseData[headers[colNumber]] = value;
          }
        });

        // Store the test case data
        testData[sheetName][testCaseName] = testCaseData;
      });
    });

    // Write the JSON file
    fs.writeFileSync(this.jsonFilePath, JSON.stringify(testData, null, 2));
    console.log(`Successfully converted Excel to JSON: ${this.jsonFilePath}`);

    return this.jsonFilePath;
  }

  /**
   * Gets the path to the generated JSON file
   */
  getJsonFilePath(): string {
    return this.jsonFilePath;
  }
}
