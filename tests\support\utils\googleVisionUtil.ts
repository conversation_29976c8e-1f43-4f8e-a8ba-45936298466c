import * as fs from 'fs';
import * as path from 'path';
import * as vision from '@google-cloud/vision';
import logger from './logger.util.ts';
import elementActions from '../actions/element.actions.ts';

export class GoogleVisionUtil {
    private credentialsPath: string;
    private client: vision.ImageAnnotatorClient;

    /**
     * Initialize Google Vision client with credentials
     * @param credentialsPath Optional path to custom credentials JSON file
     */
    constructor(credentialsPath?: string) {
        this.credentialsPath = credentialsPath || path.resolve(process.cwd(), './tests/resources/google-key/google-image-to-text-credentials.json');

        // Set environment variable for Google credentials
        process.env.GOOGLE_APPLICATION_CREDENTIALS = this.credentialsPath;

        // Initialize the Vision client
        this.client = new vision.ImageAnnotatorClient();
        logger.info('Google Vision client initialized');
    }

    /**
     * Detects text in an image file
     * @param imagePath Path to the image file
     * @param deleteAfter Whether to delete the image after processing (default: false)
     * @returns Detected text as a string
     */
    public async detectText(imagePath: string, deleteAfter: boolean = false): Promise<string> {
        try {
            logger.info(`Detecting text in image: ${imagePath}`);

            if (!fs.existsSync(imagePath)) {
                throw new Error(`Image file not found: ${imagePath}`);
            }

            // Read the image file
            const imageFile = fs.readFileSync(imagePath);

            // Call the Vision API
            const [result] = await this.client.textDetection(imageFile);
            const textAnnotations = result.textAnnotations || [];

            let resultText = '';
            if (textAnnotations.length > 0) {
                resultText = textAnnotations[0].description || '';
                logger.info(`Text detected: ${resultText.substring(0, 50)}${resultText.length > 50 ? '...' : ''}`);
            } else {
                logger.info('No text detected in the image');
            }

            // Delete the image file if requested
            if (deleteAfter) {
                fs.unlinkSync(imagePath);
                logger.info(`Deleted image file: ${imagePath}`);
            }

            return resultText;
        } catch (error) {
            logger.error(`Error detecting text: ${error}`);
            throw error;
        }
    }

    /**
     * Takes a screenshot of an element or page and detects text in it
     * @param element WebdriverIO element or selector string
     * @param screenshotName Optional name for the screenshot file
     * @returns Detected text as a string
     */
    public async detectTextFromElement(
        element: ChainablePromiseElement,
        screenshotName?: string,
    ): Promise<string> {
        try {
            await elementActions.waitForDisplayed(element);
            const timestamp = new Date().getTime();
            const fileName = screenshotName || `screenshot_${timestamp}.png`;
            const screenshotPath = path.resolve(process.cwd(), 'reports/screenshots', fileName);

            // Ensure directory exists
            const dir = path.dirname(screenshotPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Take screenshot
            if (element) {
                // Handle either element object or selector string
                if (typeof element === 'string') {
                    logger.info(`Taking screenshot of element with selector: ${element}`);
                    await $(element).saveScreenshot(screenshotPath);
                } else {
                    logger.info('Taking screenshot of provided element');
                    await element.saveScreenshot(screenshotPath);
                }
            } else {
                logger.info('Taking full page screenshot');
                await browser.saveScreenshot(screenshotPath);
            }

            // Detect text in the screenshot
            const text = await this.detectText(screenshotPath, true);
            return text;
        } catch (error) {
            logger.error(`Error in detectTextFromElement: ${error}`);
            throw error;
        }
    }

    /**
     * Normalizes text by removing extra whitespace and line breaks
     * @param text Text to normalize
     * @returns Normalized text
     */
    public normalizeText(text: string): string {
        return text
            .replace(/\r\n/g, ' ')  // Replace Windows line breaks with spaces
            .replace(/\n/g, ' ')    // Replace Unix line breaks with spaces
            .replace(/\s+/g, ' ')   // Replace multiple spaces with a single space
            .trim();                // Remove leading/trailing whitespace
    }

    /**
     * Detects text from element and normalizes it to a single line
     * @param element WebdriverIO element or selector string
     * @param screenshotName Optional screenshot filename
     * @returns Normalized single-line text
     */
    public async detectTextFromElementSingleLine(
        element: ChainablePromiseElement,
        screenshotName?: string,
    ): Promise<string> {
        const text = await this.detectTextFromElement(element, screenshotName);
        console.log('Normalized detected text:', text);
        return this.normalizeText(text);
    }

    /**
     * Extracts only alphanumeric characters and spaces from text
     * @param text Text to process
     * @returns Alphanumeric text
     */
    public alphanumericOnly(text: string): string {
        return text.replace(/[^a-zA-Z0-9\s]/g, '').trim();
    }
}

export default GoogleVisionUtil;