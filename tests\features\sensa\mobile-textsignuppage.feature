Feature: Text Sign-Up Page Validation.

    Scenario Outline: Vaidate Success Message when entered valid Mobile Number in Sign Up Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on Text Signup link
        Then The user Validates Text Sign-Up Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then The user enters valid Mobile Number as <mobileno> and validates success message as <succesmmsg>
        Then The user validates that successfully logged out

        @SensaValidMobileNumber_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | mobileno     | succesmmsg                                                        | filename              | sheetname | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | ************ | There was an error submitting the phone number. Please try again. | aem-mobile-sensa.json | SignUp    | Validate Text Sign-Up Pages |

    Scenario Outline: Vaidate Error Message when entered invalid Mobile Number in Sign Up Page for <Brand>
        Given The user is on the login page for <Brand> with login <URL>
        When The user login with valid user id <Username> and password <Password>
        Then The user should be able to login to the application successfully
        When The user clicks on Text Signup link
        Then The user enters invalid Mobile Number as <mobileno> and validates error message as <error>
        Then The user validates that successfully logged out

        @SensaInValidMobileNumber_Validation_QA 
        Examples:
            | Brand | URL                             | Username                           | Password  | mobileno     | error                                                       | filename              | sheetname | scenarioname                |
            | Sensa | https://aem-stage.sensavape.com | <EMAIL> | Password1 | ************ | Please provide a valid phone number in xxx-xxx-xxxx format. | aem-mobile-sensa.json | SignUp    | Validate Text Sign-Up Pages |

